/**
 * SQL queries for 'Stoklar' module initialization and operations
 */
export const query = `
-- Table: Stoklar
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Stoklar' and xtype='U')
BEGIN
    CREATE TABLE Stoklar (
        id INT PRIMARY KEY IDENTITY(1,1),
        kodu VARCHAR(25) NOT NULL,
        adi VARCHAR(100) NOT NULL,
        anagrup VARCHAR(25),
        anaGrupAdi VARCHAR(100),
        altgrup VARCHAR(25),
        altGrupAdi VARCHAR(100),
        kategori VARCHAR(25),
        kategori<PERSON><PERSON> VARCHAR(100),
        reyon VARCHAR(25),
        reyonAdi VARCHAR(100),
        marka VARCHAR(25),
        marka<PERSON>di VARCHAR(100),
        kisa_adi VARCHAR(100),
        yabanci_adi VARCHAR(100),
        pasif BIT DEFAULT 0,
        isk_yapilamaz BIT DEFAULT 0,
        iskonto DECIMAL(18,4) DEFAULT 0,
        fiyat_kasada_belirlenir BIT DEFAULT 0,
        toptan_vergi_yuzde DECIMAL(18,4) DEFAULT 0,
        perakende_vergi_yuzde DECIMAL(18,4) DEFAULT 0,
        detay_takip BIT DEFAULT 0,
        doviz INT DEFAULT 0,
        doviz_adi VARCHAR(50),
        malkabul_dursun BIT DEFAULT 0,
        mensei VARCHAR(50),
        perakende_vergi INT DEFAULT 0,
        perakende_vergi_adi VARCHAR(50),
        satis_dursun BIT DEFAULT 0,
        siparis_dursun BIT DEFAULT 0,
        terazi_skt BIT DEFAULT 0,
        toptan_vergi INT DEFAULT 0,
        toptan_vergi_adi VARCHAR(50),
        yerli BIT DEFAULT 1,
        veritabani_id VARCHAR(37) NOT NULL,
        logoRef INT,
        error NVARCHAR(MAX),
        logo_rest_data VARCHAR(MAX) NULL,
        logo_sql_data VARCHAR(MAX) NULL,
        createdAt DATETIME2 NOT NULL DEFAULT GETDATE(),
        updatedAt DATETIME2 NOT NULL DEFAULT GETDATE()
    );
    PRINT 'Stoklar tablosu başarıyla oluşturuldu';
END
ELSE
BEGIN
    PRINT 'Stoklar tablosu zaten mevcut';
END;

-- Table: StokBirimleri
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='StokBirimleri' and xtype='U')
BEGIN
    CREATE TABLE StokBirimleri (
        id INT PRIMARY KEY IDENTITY(1,1),
        stokId INT NOT NULL,
        birimSira INT NOT NULL,
        birim VARCHAR(10) NOT NULL,
        katsayi VARCHAR(20) DEFAULT '1',
        barkodSira INT DEFAULT 0,
        barkod VARCHAR(50),
        logo_rest_data VARCHAR(MAX) NULL,
        logo_sql_data VARCHAR(MAX) NULL,
        createdAt DATETIME2 NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (stokId) REFERENCES Stoklar(id)
    );
    PRINT 'StokBirimleri tablosu başarıyla oluşturuldu';
END
ELSE
BEGIN
    PRINT 'StokBirimleri tablosu zaten mevcut';
END;

-- Table: StokFiyatlari
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='StokFiyatlari' and xtype='U')
BEGIN
    CREATE TABLE StokFiyatlari (
        id INT PRIMARY KEY IDENTITY(1,1),
        stokId INT NOT NULL,
        listeNo VARCHAR(10) NOT NULL,
        listeAdi VARCHAR(100),
        birim INT DEFAULT 1,
        depoNo INT DEFAULT 0,
        doviz INT DEFAULT 0,
        dovizAdi VARCHAR(50),
        kur DECIMAL(18,6) DEFAULT 1,
        satisFiyati DECIMAL(18,4) NOT NULL,
        kdvDurumu BIT DEFAULT 0,
        logo_rest_data VARCHAR(MAX) NULL,
        logo_sql_data VARCHAR(MAX) NULL,
        createdAt DATETIME2 NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (stokId) REFERENCES Stoklar(id)
    );
    PRINT 'StokFiyatlari tablosu başarıyla oluşturuldu';
END
ELSE
BEGIN
    PRINT 'StokFiyatlari tablosu zaten mevcut';
END;
`
