/**
 * Logo Entegrasyon Loglama Sistemi - Veritabanı Migration
 * 
 * Bu migration, tüm iş modülü tablolarına Logo entegrasyon verilerini
 * JSON formatında saklayacak sütunları ekler.
 */
export const query = `
BEGIN TRY
    PRINT 'Logo entegrasyon loglama sütunları ekleniyor...';

    -- CariHesaplar tablosuna logo loglama sütunları ekleme
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'CariHesaplar' AND COLUMN_NAME = 'logo_rest_data')
    BEGIN
        ALTER TABLE CariHesaplar ADD logo_rest_data VARCHAR(MAX) NULL;
        PRINT 'CariHesaplar.logo_rest_data sütunu eklendi';
    END
    ELSE
    BEGIN
        PRINT 'CariHesaplar.logo_rest_data sütunu zaten mevcut';
    END;

    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'CariHesaplar' AND COLUMN_NAME = 'logo_sql_data')
    BEGIN
        ALTER TABLE CariHesaplar ADD logo_sql_data VARCHAR(MAX) NULL;
        PRINT 'CariHesaplar.logo_sql_data sütunu eklendi';
    END
    ELSE
    BEGIN
        PRINT 'CariHesaplar.logo_sql_data sütunu zaten mevcut';
    END;

    -- SatisFaturalari tablosuna logo loglama sütunları ekleme
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SatisFaturalari' AND COLUMN_NAME = 'logo_rest_data')
    BEGIN
        ALTER TABLE SatisFaturalari ADD logo_rest_data VARCHAR(MAX) NULL;
        PRINT 'SatisFaturalari.logo_rest_data sütunu eklendi';
    END
    ELSE
    BEGIN
        PRINT 'SatisFaturalari.logo_rest_data sütunu zaten mevcut';
    END;

    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SatisFaturalari' AND COLUMN_NAME = 'logo_sql_data')
    BEGIN
        ALTER TABLE SatisFaturalari ADD logo_sql_data VARCHAR(MAX) NULL;
        PRINT 'SatisFaturalari.logo_sql_data sütunu eklendi';
    END
    ELSE
    BEGIN
        PRINT 'SatisFaturalari.logo_sql_data sütunu zaten mevcut';
    END;

    -- SatinalmaFaturalari tablosuna logo loglama sütunları ekleme
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SatinalmaFaturalari' AND COLUMN_NAME = 'logo_rest_data')
    BEGIN
        ALTER TABLE SatinalmaFaturalari ADD logo_rest_data VARCHAR(MAX) NULL;
        PRINT 'SatinalmaFaturalari.logo_rest_data sütunu eklendi';
    END
    ELSE
    BEGIN
        PRINT 'SatinalmaFaturalari.logo_rest_data sütunu zaten mevcut';
    END;

    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SatinalmaFaturalari' AND COLUMN_NAME = 'logo_sql_data')
    BEGIN
        ALTER TABLE SatinalmaFaturalari ADD logo_sql_data VARCHAR(MAX) NULL;
        PRINT 'SatinalmaFaturalari.logo_sql_data sütunu eklendi';
    END
    ELSE
    BEGIN
        PRINT 'SatinalmaFaturalari.logo_sql_data sütunu zaten mevcut';
    END;

    -- SatisFaturalariSatirlari tablosuna logo loglama sütunları ekleme
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'SatisFaturalariSatirlari')
    BEGIN
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SatisFaturalariSatirlari' AND COLUMN_NAME = 'logo_rest_data')
        BEGIN
            ALTER TABLE SatisFaturalariSatirlari ADD logo_rest_data VARCHAR(MAX) NULL;
            PRINT 'SatisFaturalariSatirlari.logo_rest_data sütunu eklendi';
        END
        ELSE
        BEGIN
            PRINT 'SatisFaturalariSatirlari.logo_rest_data sütunu zaten mevcut';
        END;

        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SatisFaturalariSatirlari' AND COLUMN_NAME = 'logo_sql_data')
        BEGIN
            ALTER TABLE SatisFaturalariSatirlari ADD logo_sql_data VARCHAR(MAX) NULL;
            PRINT 'SatisFaturalariSatirlari.logo_sql_data sütunu eklendi';
        END
        ELSE
        BEGIN
            PRINT 'SatisFaturalariSatirlari.logo_sql_data sütunu zaten mevcut';
        END;
    END;

    -- SatinalmaFaturalariSatirlari tablosuna logo loglama sütunları ekleme
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'SatinalmaFaturalariSatirlari')
    BEGIN
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SatinalmaFaturalariSatirlari' AND COLUMN_NAME = 'logo_rest_data')
        BEGIN
            ALTER TABLE SatinalmaFaturalariSatirlari ADD logo_rest_data VARCHAR(MAX) NULL;
            PRINT 'SatinalmaFaturalariSatirlari.logo_rest_data sütunu eklendi';
        END
        ELSE
        BEGIN
            PRINT 'SatinalmaFaturalariSatirlari.logo_rest_data sütunu zaten mevcut';
        END;

        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SatinalmaFaturalariSatirlari' AND COLUMN_NAME = 'logo_sql_data')
        BEGIN
            ALTER TABLE SatinalmaFaturalariSatirlari ADD logo_sql_data VARCHAR(MAX) NULL;
            PRINT 'SatinalmaFaturalariSatirlari.logo_sql_data sütunu eklendi';
        END
        ELSE
        BEGIN
            PRINT 'SatinalmaFaturalariSatirlari.logo_sql_data sütunu zaten mevcut';
        END;
    END;

    -- Diğer iş modülü tablolarına da aynı sütunları ekleme (gelecekte kullanım için)
    DECLARE @TableName NVARCHAR(128);
    DECLARE @SQL NVARCHAR(MAX);
    
    -- Potansiyel iş modülü tabloları listesi
    DECLARE table_cursor CURSOR FOR
    SELECT TABLE_NAME 
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_TYPE = 'BASE TABLE' 
    AND TABLE_NAME IN ('Stoklar', 'Reyonlar', 'AltGruplar', 'AnaGruplar', 'Kategoriler', 'Markalar', 'CariPersonel');

    OPEN table_cursor;
    FETCH NEXT FROM table_cursor INTO @TableName;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        -- logo_rest_data sütunu kontrolü ve eklenmesi
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = @TableName AND COLUMN_NAME = 'logo_rest_data')
        BEGIN
            SET @SQL = 'ALTER TABLE ' + @TableName + ' ADD logo_rest_data VARCHAR(MAX) NULL';
            EXEC sp_executesql @SQL;
            PRINT @TableName + '.logo_rest_data sütunu eklendi';
        END
        ELSE
        BEGIN
            PRINT @TableName + '.logo_rest_data sütunu zaten mevcut';
        END;

        -- logo_sql_data sütunu kontrolü ve eklenmesi
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = @TableName AND COLUMN_NAME = 'logo_sql_data')
        BEGIN
            SET @SQL = 'ALTER TABLE ' + @TableName + ' ADD logo_sql_data VARCHAR(MAX) NULL';
            EXEC sp_executesql @SQL;
            PRINT @TableName + '.logo_sql_data sütunu eklendi';
        END
        ELSE
        BEGIN
            PRINT @TableName + '.logo_sql_data sütunu zaten mevcut';
        END;

        FETCH NEXT FROM table_cursor INTO @TableName;
    END;

    CLOSE table_cursor;
    DEALLOCATE table_cursor;

    PRINT 'Logo entegrasyon loglama migration başarıyla tamamlandı';

END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();

    PRINT 'Logo loglama migration sırasında hata oluştu:';
    PRINT @ErrorMessage;
    
    THROW;
END CATCH;`
