import type { CariHesapRequest, CariHesapResponseData, ClCardParams, LogoCariHesapRequest } from '../models/types.ts'
import consola from 'consola'
import DbService from '../../shared/services/db-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'
import LogoRestService from './logo-rest-service.ts'
import LogoSqlService from './logo-sql-service.ts'

export async function sendCariHesap({
  cariHesapData,
  veritabaniId,
}: {
  cariHesapData: CariHesapRequest
  veritabaniId: string
}): Promise<CariHesapResponseData> {
  try {
    const logoConfig = await getLogoConfigById(veritabaniId)

    // 1. Check if customer already exists
    const existingCard = await checkExistingCariHesap({ code: cariHesapData.kodu, veritabaniId })
    if (existingCard)
      return existingCard

    // 2. Process based on REST flag
    return logoConfig.erp.rest_settings.use_rest
      ? await processWithRest({ cariHesapData, veritabaniId })
      : await processWithoutRest({ cariHesapData, veritabaniId, logoConfig })
  }
  catch (error) {
    consola.error('Cari kart gönderilirken hata oluştu:', error)
    throw error
  }
}

async function checkExistingCariHesap({
  code,
  veritabaniId,
}: {
  code: string
  veritabaniId: string
}): Promise<CariHesapResponseData | null> {
  const existingCard = await LogoSqlService.getcariHesapDetails({
    code,
    veritabaniId,
  })

  if (existingCard) {
    return {
      code: existingCard.code,
      vkVeyaTckNo: existingCard.vkVeyaTckNo,
      logicalref: existingCard.logicalref,
      isExisting: true,
      veritabani_id: veritabaniId,
    }
  }

  return null
}

async function processWithoutRest({
  cariHesapData,
  veritabaniId,
  logoConfig,
}: {
  cariHesapData: CariHesapRequest
  veritabaniId: string
  logoConfig: any
}): Promise<CariHesapResponseData> {
  // Transform to Logo format
  const readyCustomerToPost = await transformToLogoFormat({ cariHesapData })

  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
  const dbConnection = await DbService.getConnection('db')
  const guid = await LogoSqlService.generateUniqueGuid(logoConnection, dbConnection, logoConfig.erp.firma_numarasi)

  // Set default values and prepare params for CLCARD insert
  const clcardParams = prepareClCardParams({ cariHesapData, readyCustomerToPost, guid })

  // Store record in CariHesaplarSql for tracking
  await LogoSqlService.insertCariHesaplarSql({
    veritabaniId,
    params: {
      ...clcardParams,
      veritabani_id: veritabaniId,
    },
  })

  // Insert to CLCARD table
  const logicalref = await LogoSqlService.insertClCard({ veritabaniId, params: clcardParams })

  // Insert into CariHesaplar with correct logoRef
  await insertCariHesaplar({ cariHesapData, veritabaniId, logicalref })

  return {
    code: cariHesapData.kodu,
    vkVeyaTckNo: cariHesapData.vkVeyaTckNo,
    logicalref,
    isExisting: false,
    veritabani_id: veritabaniId,
  }
}

async function processWithRest({
  cariHesapData,
  veritabaniId,
}: {
  cariHesapData: CariHesapRequest
  veritabaniId: string
}): Promise<CariHesapResponseData> {
  // Create cariHesap record first - without LOGO references
  const cardId = await insertCariHesaplar({ cariHesapData, veritabaniId, logicalref: 0 })
  let accessToken = null
  let logicalref = null

  try {
    // Transform to Logo format
    const readyCustomerToPost = await transformToLogoFormat({ cariHesapData })

    // Get a token from Logo REST API
    accessToken = await LogoRestService.getToken({ veritabaniId })

    // Send the cari hesaplar to Logo REST API
    const response = await LogoRestService.postCariHesap({
      accessToken,
      cariHesap: readyCustomerToPost,
      veritabaniId,
    })
    logicalref = response.INTERNAL_REFERENCE

    // Update the card record with logo reference
    await LogoSqlService.updateCariHesaplarLogoRef({
      veritabaniId,
      params: { id: cardId, logoRef: logicalref },
    })

    // Save Logo Cari hesaplar data
    await saveLogoCariHesapData({ cardId, readyCustomerToPost, veritabaniId })

    return {
      code: cariHesapData.kodu,
      vkVeyaTckNo: cariHesapData.vkVeyaTckNo,
      logicalref,
      isExisting: false,
      veritabani_id: veritabaniId,
    }
  }
  catch (error) {
    await handleRestError({ _accessToken: accessToken, cardId, error, veritabaniId })
    throw error
  }
  finally {
    if (accessToken) {
      try {
        await LogoRestService.revokeToken({ accessToken, veritabaniId })
      }
      catch (revokeError) {
        consola.error('Token revoke error:', revokeError)
      }
    }
  }
}

async function insertCariHesaplar({
  cariHesapData,
  veritabaniId,
  logicalref,
}: {
  cariHesapData: CariHesapRequest
  veritabaniId: string
  logicalref: number
}): Promise<number> {
  return LogoSqlService.insertCariHesaplar({
    veritabaniId,
    params: {
      kodu: cariHesapData.kodu,
      vkVeyaTckNo: cariHesapData.vkVeyaTckNo,
      unvan: cariHesapData.unvan,
      ad: cariHesapData.ad,
      soyad: cariHesapData.soyad,
      adres: cariHesapData.adres,
      il: cariHesapData.il,
      ilce: cariHesapData.ilce,
      ulke: cariHesapData.ulke,
      ulkeKodu: cariHesapData.ulkeKodu,
      email: cariHesapData.email,
      postaKodu: cariHesapData.postaKodu,
      ozelKod: cariHesapData.ozelKod,
      logoRef: logicalref,
    },
  })
}

async function saveLogoCariHesapData({
  cardId,
  readyCustomerToPost,
  veritabaniId,
}: {
  cardId: number
  readyCustomerToPost: LogoCariHesapRequest
  veritabaniId: string
}): Promise<void> {
  const logoCariHesap = {
    cariHesapId: cardId,
    code: readyCustomerToPost.CODE,
    title: readyCustomerToPost.TITLE,
    name: readyCustomerToPost.NAME,
    surname: readyCustomerToPost.SURNAME,
    address1: readyCustomerToPost.ADDRESS1,
    address2: readyCustomerToPost.ADDRESS2,
    town: readyCustomerToPost.TOWN,
    city: readyCustomerToPost.CITY,
    country: readyCustomerToPost.COUNTRY,
    countryCode: readyCustomerToPost.COUNTRY_CODE,
    postalCode: readyCustomerToPost.POSTAL_CODE,
    auxilCode: readyCustomerToPost.AUXIL_CODE,
    email: readyCustomerToPost.E_MAIL,
    taxId: readyCustomerToPost.TAX_ID,
    tcId: readyCustomerToPost.TCKNO,
    accountType: readyCustomerToPost.ACCOUNT_TYPE,
    acceptEInv: readyCustomerToPost.ACCEPT_EINV,
    postLabel: readyCustomerToPost.POST_LABEL,
    senderLabel: readyCustomerToPost.SENDER_LABEL,
    profileId: readyCustomerToPost.PROFILE_ID,
    insteadOfDispatch: readyCustomerToPost.INSTEAD_OF_DISPATCH,
  }

  await LogoSqlService.saveLogoCariHesap({ logoCariHesap, veritabaniId })
}

async function handleRestError({
  _accessToken,
  cardId,
  error,
  veritabaniId,
}: {
  _accessToken: string | null
  cardId: number
  error: unknown
  veritabaniId: string
}): Promise<void> {
  const errorMessage = error instanceof Error ? `${error.message} ${error.cause}` : 'Bilinmeyen hata'

  if (error instanceof Error) {
    error.message = `${error.message} ${error.cause}`
  }

  await LogoSqlService.updateCariHesaplarError({
    veritabaniId,
    params: { id: cardId, error: errorMessage },
  })
}

function prepareClCardParams({
  cariHesapData,
  readyCustomerToPost,
  guid,
}: {
  cariHesapData: CariHesapRequest
  readyCustomerToPost: LogoCariHesapRequest
  guid: string
}): ClCardParams {
  const clcardInsertData = {
    COUNTRY: cariHesapData.ulke || 'TÜRKİYE',
    COUNTRY_CODE: cariHesapData.ulkeKodu || 'TR',
    POSTAL_CODE: cariHesapData.postaKodu || '',
    AUXIL_CODE: cariHesapData.ozelKod || '',
    E_MAIL: cariHesapData.email || '',
    PURCHBRWS: 1,
    SALESBRWS: 1,
    IMPBRWS: 1,
    EXPBRWS: 1,
    FINBRWS: 1,
    ISPERSCOMP: readyCustomerToPost.PERSCOMPANY || 0,
    INSTEADOFDESP: readyCustomerToPost.INSTEAD_OF_DISPATCH || 1,
  }

  return {
    CARDTYPE: readyCustomerToPost.ACCOUNT_TYPE || 3,
    DEFINITION_: readyCustomerToPost.TITLE,
    SPECODE: readyCustomerToPost.AUXIL_CODE || '',
    ADDR1: readyCustomerToPost.ADDRESS1 || '',
    ADDR2: readyCustomerToPost.ADDRESS2 || '',
    CITY: readyCustomerToPost.CITY || '',
    COUNTRY: clcardInsertData.COUNTRY,
    POSTCODE: clcardInsertData.POSTAL_CODE,
    EMAILADDR: clcardInsertData.E_MAIL,
    CAPIBLOCK_CREATEDMIN: 0,
    PURCHBRWS: clcardInsertData.PURCHBRWS,
    SALESBRWS: clcardInsertData.SALESBRWS,
    IMPBRWS: clcardInsertData.IMPBRWS,
    EXPBRWS: clcardInsertData.EXPBRWS,
    FINBRWS: clcardInsertData.FINBRWS,
    ISPERSCOMP: clcardInsertData.ISPERSCOMP,
    CODE: readyCustomerToPost.CODE,
    TAXNR: readyCustomerToPost.TAX_ID || '',
    TCKNO: readyCustomerToPost.TCKNO || '',
    NAME: readyCustomerToPost.NAME || '',
    SURNAME: readyCustomerToPost.SURNAME || '',
    INSTEADOFDESP: clcardInsertData.INSTEADOFDESP,
    TOWN: readyCustomerToPost.TOWN || '',
    COUNTRYCODE: clcardInsertData.COUNTRY_CODE,
    GUID: guid,
    ACTIVE: 0,
    CYPHCODE: '',
    TELNRS1: '',
    TELNRS2: '',
    FAXNR: '',
    TAXOFFICE: '',
    INCHARGE: '',
    WEBADDR: '',
    WARNEMAILADDR: '',
    WARNFAXNR: '',
    VATNR: '',
    BANKBRANCHS1: '',
    BANKBRANCHS2: '',
    BANKBRANCHS3: '',
    BANKBRANCHS4: '',
    BANKBRANCHS5: '',
    BANKBRANCHS6: '',
    BANKBRANCHS7: '',
    BANKACCOUNTS1: '',
    BANKACCOUNTS2: '',
    BANKACCOUNTS3: '',
    BANKACCOUNTS4: '',
    BANKACCOUNTS5: '',
    BANKACCOUNTS6: '',
    BANKACCOUNTS7: '',
    DELIVERYMETHOD: '',
    DELIVERYFIRM: '',
    EDINO: '',
    TRADINGGRP: '',
    PPGROUPCODE: '',
    TAXOFFCODE: '',
    TOWNCODE: '',
    DISTRICTCODE: '',
    DISTRICT: '',
    CITYCODE: '',
    ORDSENDEMAILADDR: '',
    ORDSENDFAXNR: '',
    DSPSENDEMAILADDR: '',
    DSPSENDFAXNR: '',
    INVSENDEMAILADDR: '',
    INVSENDFAXNR: '',
    SUBSCRIBEREXT: '',
    AUTOPAIDBANK: '',
    STORECREDITCARDNO: '',
    LOGOID: '',
    EXPREGNO: '',
    EXPDOCNO: '',
    LTRSENDEMAILADDR: '',
    LTRSENDFAXNR: '',
    CELLPHONE: '',
    STATECODE: '',
    STATENAME: '',
    TELCODES1: '',
    TELCODES2: '',
    FAXCODE: '',
    ORGLOGOID: '',
    SPECODE2: '',
    SPECODE3: '',
    SPECODE4: '',
    SPECODE5: '',
    OFFSENDEMAILADDR: '',
    OFFSENDFAXNR: '',
    BANKNAMES1: '',
    BANKNAMES2: '',
    BANKNAMES3: '',
    BANKNAMES4: '',
    BANKNAMES5: '',
    BANKNAMES6: '',
    BANKNAMES7: '',
    MAPID: '',
    LONGITUDE: '',
    LATITUTE: '',
    CITYID: '',
    TOWNID: '',
    BANKIBANS1: '',
    BANKIBANS2: '',
    BANKIBANS3: '',
    BANKIBANS4: '',
    BANKIBANS5: '',
    BANKIBANS6: '',
    BANKIBANS7: '',
    EXTSENDEMAILADDR: '',
    EXTSENDFAXNR: '',
    BANKBICS1: '',
    BANKBICS2: '',
    BANKBICS3: '',
    BANKBICS4: '',
    BANKBICS5: '',
    BANKBICS6: '',
    BANKBICS7: '',
    INCHARGE2: '',
    INCHARGE3: '',
    EMAILADDR2: '',
    EMAILADDR3: '',
    EINVOICEID: '',
    BANKBCURRENCY1: '',
    BANKBCURRENCY2: '',
    BANKBCURRENCY3: '',
    BANKBCURRENCY4: '',
    BANKBCURRENCY5: '',
    BANKBCURRENCY6: '',
    BANKBCURRENCY7: '',
    BANKCORRPACC1: '',
    BANKCORRPACC2: '',
    BANKCORRPACC3: '',
    BANKCORRPACC4: '',
    BANKCORRPACC5: '',
    BANKCORRPACC6: '',
    BANKCORRPACC7: '',
    BANKVOEN1: '',
    BANKVOEN2: '',
    BANKVOEN3: '',
    BANKVOEN4: '',
    BANKVOEN5: '',
    BANKVOEN6: '',
    BANKVOEN7: '',
    DEFINITION2: '',
    TELEXTNUMS1: '',
    TELEXTNUMS2: '',
    FAXEXTNUM: '',
    FACEBOOKURL: '',
    TWITTERURL: '',
    APPLEID: '',
    SKYPEID: '',
    GLOBALID: '',
    ADRESSNO: '',
    POSTLABELCODE: '',
    SENDERLABELCODE: '',
    FBSSENDEMAILADDR: '',
    FBSSENDFAXNR: '',
    FBASENDEMAILADDR: '',
    FBASENDFAXNR: '',
    EARCEMAILADDR1: '',
    EARCEMAILADDR2: '',
    EARCEMAILADDR3: '',
    POSTLABELCODEDESP: '',
    SENDERLABELCODEDESP: '',
    EXIMSENDEMAILADDR: '',
    EXIMSENDFAXNR: '',
    INCHTELCODES1: '',
    INCHTELCODES2: '',
    INCHTELCODES3: '',
    INCHTELNRS1: '',
    INCHTELNRS2: '',
    INCHTELNRS3: '',
    INCHTELEXTNUMS1: '',
    INCHTELEXTNUMS2: '',
    INCHTELEXTNUMS3: '',
    MERSISNO: '',
    COMMRECORDNO: '',
    WHATSAPPID: '',
    LINKEDINURL: '',
    INSTAGRAMURL: '',
    DISCRATE: 0,
    EXTENREF: 0,
    PAYMENTREF: 0,
    WARNMETHOD: 0,
    CLANGUAGE: 0,
    BLOCKED: 0,
    CCURRENCY: 0,
    TEXTINC: 0,
    SITEID: 0,
    RECSTATUS: 0,
    ORGLOGICREF: 0,
    CAPIBLOCK_MODIFIEDBY: 0,
    CAPIBLOCK_MODIFIEDHOUR: 0,
    CAPIBLOCK_MODIFIEDMIN: 0,
    CAPIBLOCK_MODIFIEDSEC: 0,
    PAYMENTPROC: 0,
    CRATEDIFFPROC: 0,
    WFSTATUS: 0,
    PPGROUPREF: 0,
    ORDSENDMETHOD: 0,
    DSPSENDMETHOD: 0,
    INVSENDMETHOD: 0,
    SUBSCRIBERSTAT: 0,
    PAYMENTTYPE: 0,
    LASTSENDREMLEV: 0,
    EXTACCESSFLAGS: 0,
    ORDSENDFORMAT: 0,
    DSPSENDFORMAT: 0,
    INVSENDFORMAT: 0,
    REMSENDFORMAT: 0,
    CLORDFREQ: 0,
    ORDDAY: 0,
    LIDCONFIRMED: 0,
    EXPBUSTYPREF: 0,
    INVPRINTCNT: 0,
    PIECEORDINFLICT: 0,
    COLLECTINVOICING: 0,
    EBUSDATASENDTYPE: 0,
    INISTATUSFLAGS: 0,
    SLSORDERSTATUS: 0,
    SLSORDERPRICE: 0,
    LTRSENDMETHOD: 0,
    LTRSENDFORMAT: 0,
    IMAGEINC: 0,
    SAMEITEMCODEUSE: 0,
    WFLOWCRDREF: 0,
    PARENTCLREF: 0,
    LOWLEVELCODES2: 0,
    LOWLEVELCODES3: 0,
    LOWLEVELCODES4: 0,
    LOWLEVELCODES5: 0,
    LOWLEVELCODES6: 0,
    LOWLEVELCODES7: 0,
    LOWLEVELCODES8: 0,
    LOWLEVELCODES9: 0,
    LOWLEVELCODES10: 0,
    ADDTOREFLIST: 0,
    TEXTREFTR: 0,
    TEXTREFEN: 0,
    ARPQUOTEINC: 0,
    CLCRM: 0,
    GRPFIRMNR: 0,
    CONSCODEREF: 0,
    OFFSENDMETHOD: 0,
    OFFSENDFORMAT: 0,
    EBANKNO: 0,
    LOANGRPCTRL: 0,
    LDXFIRMNR: 0,
    EXTSENDMETHOD: 0,
    EXTSENDFORMAT: 0,
    CASHREF: 0,
    USEDINPERIODS: 0,
    RSKLIMCR: 0,
    RSKDUEDATECR: 0,
    RSKAGINGCR: 0,
    RSKAGINGDAY: 0,
    ACCEPTEINV: 0,
    PROFILEID: 0,
    PURCORDERSTATUS: 0,
    PURCORDERPRICE: 0,
    ISFOREIGN: 0,
    SHIPBEGTIME1: 0,
    SHIPBEGTIME2: 0,
    SHIPBEGTIME3: 0,
    SHIPENDTIME1: 0,
    SHIPENDTIME2: 0,
    SHIPENDTIME3: 0,
    DBSLIMIT1: 0,
    DBSLIMIT2: 0,
    DBSLIMIT3: 0,
    DBSLIMIT4: 0,
    DBSLIMIT5: 0,
    DBSLIMIT6: 0,
    DBSLIMIT7: 0,
    DBSTOTAL1: 0,
    DBSTOTAL2: 0,
    DBSTOTAL3: 0,
    DBSTOTAL4: 0,
    DBSTOTAL5: 0,
    DBSTOTAL6: 0,
    DBSTOTAL7: 0,
    DBSBANKNO1: 0,
    DBSBANKNO2: 0,
    DBSBANKNO3: 0,
    DBSBANKNO4: 0,
    DBSBANKNO5: 0,
    DBSBANKNO6: 0,
    DBSBANKNO7: 0,
    DBSRISKCNTRL1: 0,
    DBSRISKCNTRL2: 0,
    DBSRISKCNTRL3: 0,
    DBSRISKCNTRL4: 0,
    DBSRISKCNTRL5: 0,
    DBSRISKCNTRL6: 0,
    DBSRISKCNTRL7: 0,
    DBSBANKCURRENCY1: 0,
    DBSBANKCURRENCY2: 0,
    DBSBANKCURRENCY3: 0,
    DBSBANKCURRENCY4: 0,
    DBSBANKCURRENCY5: 0,
    DBSBANKCURRENCY6: 0,
    DBSBANKCURRENCY7: 0,
    EINVOICETYPE: 0,
    DUEDATECOUNT: 0,
    DUEDATELIMIT: 0,
    DUEDATETRACK: 0,
    DUEDATECONTROL1: 0,
    DUEDATECONTROL2: 0,
    DUEDATECONTROL3: 0,
    DUEDATECONTROL4: 0,
    DUEDATECONTROL5: 0,
    DUEDATECONTROL6: 0,
    DUEDATECONTROL7: 0,
    DUEDATECONTROL8: 0,
    DUEDATECONTROL9: 0,
    DUEDATECONTROL10: 0,
    DUEDATECONTROL11: 0,
    DUEDATECONTROL12: 0,
    DUEDATECONTROL13: 0,
    DUEDATECONTROL14: 0,
    DUEDATECONTROL15: 0,
    CLOSEDATECOUNT: 0,
    CLOSEDATETRACK: 0,
    DEGACTIVE: 0,
    DEGCURR: 0,
    LABELINFO: 0,
    DEFBNACCREF: 0,
    PROJECTREF: 0,
    DISCTYPE: 0,
    SENDMOD: 0,
    ISPERCURR: 0,
    CURRATETYPE: 0,
    EINVOICETYP: 0,
    FBSSENDMETHOD: 0,
    FBSSENDFORMAT: 0,
    FBASENDMETHOD: 0,
    FBASENDFORMAT: 0,
    SECTORMAINREF: 0,
    SECTORSUBREF: 0,
    PERSONELCOSTS: 0,
    FACTORYDIVNR: 0,
    FACTORYNR: 0,
    ININVENNR: 0,
    OUTINVENNR: 0,
    QTYDEPDURATION: 0,
    QTYINDEPDURATION: 0,
    OVERLAPTYPE: 0,
    OVERLAPAMNT: 0,
    OVERLAPPERC: 0,
    BROKERCOMP: 0,
    CREATEWHFICHE: 0,
    EINVCUSTOM: 0,
    SUBCONT: 0,
    ORDPRIORITY: 0,
    ACCEPTEDESP: 0,
    PROFILEIDDESP: 0,
    LABELINFODESP: 0,
    ACCEPTEINVPUBLIC: 0,
    PUBLICBNACCREF: 0,
    PAYMENTPROCBRANCH: 0,
    KVKKPERMSTATUS: 0,
    KVKKANONYSTATUS: 0,
    EXIMSENDMETHOD: 0,
    EXIMSENDFORMAT: 0,
    CLCCANDEDUCT: 0,
    DRIVERREF: 0,
    NOTIFYCRDREF: 0,
    EXCNTRYTYP: 0,
    EXCNTRYREF: 0,
    IMCNTRYTYP: 0,
    IMCNTRYREF: 0,
    EXIMPAYTYPREF: 0,
    EXIMBRBANKREF: 0,
    EXIMCUSTOMREF: 0,
    EXIMREGTYPREF: 0,
    EXIMNTFYCLREF: 0,
    EXIMCNSLTCLREF: 0,
    EXIMFRGHTCLREF: 0,
    DISPPRINTCNT: 0,
    ORDPRINTCNT: 0,
    CLPTYPEFORPPAYDT: 0,
    CLSTYPEFORPPAYDT: 0,
  }
}

async function transformToLogoFormat({ cariHesapData }: { cariHesapData: CariHesapRequest }): Promise<LogoCariHesapRequest> {
  const readyCustomerToPost: LogoCariHesapRequest = {
    ACCOUNT_TYPE: 3,
    CODE: cariHesapData.kodu,
    TITLE: cariHesapData.unvan,
    ADDRESS1: cariHesapData.adres ? cariHesapData.adres.substring(0, 200) : '',
    ADDRESS2: cariHesapData.adres ? cariHesapData.adres.substring(200, 400) : '',
    TOWN: cariHesapData.ilce || '',
    CITY: cariHesapData.il,
    COUNTRY: cariHesapData.ulke || 'TÜRKİYE',
    COUNTRY_CODE: cariHesapData.ulkeKodu || 'TR',
    POSTAL_CODE: cariHesapData.postaKodu,
    AUXIL_CODE: cariHesapData.ozelKod,
    E_MAIL: cariHesapData.email,
    PURCHBRWS: 1,
    SALESBRWS: 1,
    IMPBRWS: 1,
    EXPBRWS: 1,
    FINBRWS: 1,
  }

  if (cariHesapData.vkVeyaTckNo.length === 10) {
    readyCustomerToPost.TAX_ID = cariHesapData.vkVeyaTckNo
  }
  else if (cariHesapData.vkVeyaTckNo.length === 11) {
    readyCustomerToPost.NAME = cariHesapData.ad
    readyCustomerToPost.SURNAME = cariHesapData.soyad
    readyCustomerToPost.TCKNO = cariHesapData.vkVeyaTckNo
    readyCustomerToPost.PERSCOMPANY = 1
  }
  return readyCustomerToPost
}
