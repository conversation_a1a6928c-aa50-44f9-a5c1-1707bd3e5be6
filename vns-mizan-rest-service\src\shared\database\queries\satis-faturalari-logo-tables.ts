/**
 * SQL queries for 'satis-faturalari-logo' tracking tables initialization
 */
export const query = `
-- These tables need to be created in our application database
-- They are similar to Logo database tables but will be used for logging and tracking

-- Table a: LogoInvoice (Similar to LG_{FFF}_{DD}_INVOICE)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LogoInvoice' and xtype='U')
BEGIN
    CREATE TABLE LogoInvoice (
        LOGICALREF int IDENTITY(1,1) NOT NULL PRIMARY KEY,
        G<PERSON><PERSON><PERSON> smallint NULL,
        TRCODE smallint NULL,
        FICH<PERSON><PERSON> varchar(17) NULL,
        DATE_ datetime NULL,
        TIME_ int NULL,
        DOCODE varchar(33) NULL,
        SPECODE varchar(11) NULL,
        <PERSON><PERSON><PERSON>OD<PERSON> varchar(11) NULL,
        CLIENTREF int NULL,
        RECVREF int NULL,
        CENTERREF int NULL,
        ACCOUNTREF int NULL,
        SOURCEINDEX smallint NULL,
        SOURCECOSTGR<PERSON> smallint NULL,
        <PERSON><PERSON><PERSON>LE<PERSON> smallint NULL,
        ACCOUNTED smallint NULL,
        <PERSON><PERSON>IN<PERSON><PERSON><PERSON> smallint NULL,
        FROMKASA smallint NULL,
        ENTEGSET smallint NULL,
        VAT float NULL,
        ADDDISCOUNTS float NULL,
        TOTALDISCOUNTS float NULL,
        TOTALDISCOUNTED float NULL,
        ADDEXPENSES float NULL,
        TOTALEXPENSES float NULL,
        DISTEXPENSE float NULL,
        TOTALDEPOZITO float NULL,
        TOTALPROMOTIONS float NULL,
        VATINCGROSS float NULL,
        TOTALVAT float NULL,
        GROSSTOTAL float NULL,
        NETTOTAL float NULL,
        GENEXP1 varchar(51) NULL,
        GENEXP2 varchar(51) NULL,
        GENEXP3 varchar(51) NULL,
        GENEXP4 varchar(51) NULL,
        GENEXP5 varchar(51) NULL,
        GENEXP6 varchar(51) NULL,
        INTERESTAPP float NULL,
        TRCURR smallint NULL,
        TRRATE float NULL,
        TRNET float NULL,
        REPORTRATE float NULL,
        REPORTNET float NULL,
        ONLYONEPAYLINE smallint NULL,
        KASTRANSREF int NULL,
        PAYDEFREF int NULL,
        PRINTCNT smallint NULL,
        GVATINC smallint NULL,
        BRANCH smallint NULL,
        DEPARTMENT smallint NULL,
        ACCFICHEREF int NULL,
        ADDEXPACCREF int NULL,
        ADDEXPCENTREF int NULL,
        DECPRDIFF smallint NULL,
        CAPIBLOCK_CREATEDBY smallint NULL,
        CAPIBLOCK_CREADEDDATE datetime NULL,
        CAPIBLOCK_CREATEDHOUR smallint NULL,
        CAPIBLOCK_CREATEDMIN smallint NULL,
        CAPIBLOCK_CREATEDSEC smallint NULL,
        CAPIBLOCK_MODIFIEDBY smallint NULL,
        CAPIBLOCK_MODIFIEDDATE datetime NULL,
        CAPIBLOCK_MODIFIEDHOUR smallint NULL,
        CAPIBLOCK_MODIFIEDMIN smallint NULL,
        CAPIBLOCK_MODIFIEDSEC smallint NULL,
        SALESMANREF int NULL,
        CANCELLEDACC smallint NULL,
        SHPTYPCOD varchar(13) NULL,
        SHPAGNCOD varchar(13) NULL,
        TRACKNR varchar(65) NULL,
        GENEXCTYP smallint NULL,
        LINEEXCTYP smallint NULL,
        TRADINGGRP varchar(17) NULL,
        TEXTINC smallint NULL,
        SITEID smallint NULL,
        RECSTATUS smallint NULL,
        ORGLOGICREF int NULL,
        FACTORYNR smallint NULL,
        WFSTATUS int NULL,
        SHIPINFOREF int NULL,
        DISTORDERREF int NULL,
        SENDCNT smallint NULL,
        DLVCLIENT smallint NULL,
        COSTOFSALEFCREF int NULL,
        OPSTAT smallint NULL,
        DOCTRACKINGNR varchar(21) NULL,
        TOTALADDTAX float NULL,
        PAYMENTTYPE smallint NULL,
        INFIDX float NULL,
        ACCOUNTEDCNT smallint NULL,
        ORGLOGOID varchar(25) NULL,
        FROMEXIM smallint NULL,
        FRGTYPCOD varchar(13) NULL,
        EXIMFCTYPE smallint NULL,
        FROMORDWITHPAY smallint NULL,
        PROJECTREF int NULL,
        WFLOWCRDREF int NULL,
        STATUS smallint NULL,
        DEDUCTIONPART1 smallint NULL,
        DEDUCTIONPART2 smallint NULL,
        TOTALEXADDTAX float NULL,
        EXACCOUNTED smallint NULL,
        FROMBANK smallint NULL,
        BNTRANSREF int NULL,
        AFFECTCOLLATRL smallint NULL,
        GRPFIRMTRANS smallint NULL,
        AFFECTRISK smallint NULL,
        CONTROLINFO smallint NULL,
        POSTRANSFERINFO smallint NULL,
        TAXFREECHX smallint NULL,
        PASSPORTNO varchar(51) NULL,
        CREDITCARDNO varchar(17) NULL,
        INEFFECTIVECOST smallint NULL,
        REFLECTED smallint NULL,
        REFLACCFICHEREF int NULL,
        CANCELLEDREFLACC smallint NULL,
        CREDITCARDNUM varchar(17) NULL,
        APPROVE smallint NULL,
        APPROVEDATE datetime NULL,
        CANTCREDEDUCT smallint NULL,
        ENTRUST smallint NULL,
        DOCDATE datetime NULL,
        EINVOICE smallint NULL,
        PROFILEID smallint NULL,
        GUID varchar(37) NULL,
        ESTATUS smallint NULL,
        ESTARTDATE datetime NULL,
        EENDDATE datetime NULL,
        EDESCRIPTION varchar(51) NULL,
        EDURATION float NULL,
        EDURATIONTYPE smallint NULL,
        DEVIR smallint NULL,
        DISTADJPRICEUFRS float NULL,
        COSFCREFUFRS int NULL,
        GLOBALID varchar(51) NULL,
        TOTALSERVICES float NULL,
        FROMLEASING smallint NULL,
        CANCELEXP varchar(251) NULL,
        UNDOEXP varchar(251) NULL,
        VATEXCEPTREASON varchar(201) NULL,
        CAMPAIGNCODE varchar(25) NULL,
        CANCELDESPSINV smallint NULL,
        FROMEXCHDIFF smallint NULL,
        EXIMVAT smallint NULL,
        SERIALCODE varchar(17) NULL,
        APPCLDEDUCTLIM smallint NULL,
        EINVOICETYP smallint NULL,
        VATEXCEPTCODE varchar(11) NULL,
        ATAXEXCEPTREASON varchar(201) NULL,
        ATAXEXCEPTCODE varchar(11) NULL,
        FROMSTAFFOTHEREX smallint NULL,
        NOCALCULATE smallint NULL,
        INSTEADOFDESP smallint NULL,
        OKCFICHE smallint NULL,
        CANCELDATE datetime NULL,
        FRGTYPDESC varchar(251) NULL,
        MARKREF int NULL,
        PRINTDATE datetime NULL,
        DELIVERCODE varchar(11) NULL,
        ACCEPTEINVPUBLIC smallint NULL,
        PUBLICBNACCREF int NULL,
        TYPECODE varchar(5) NULL,
        FUTMNTHYREXPINC smallint NULL,
        DOCDETAIL smallint NULL,
        CALCADDTAXVATSEP smallint NULL,
        ESENDDATE datetime NULL,
        ELECTDOC smallint NULL,
        NOTIFYCRDREF int NULL,
        GIBACCFICHEREF int NULL,
        FROMINTEGTYPE smallint NULL,
        EPRINTCNT smallint NULL,
        RIGHTOFRETURNTYP varchar(4) NULL,
        CLNOTREFLACNTRREF int NULL,
        CLNOTREFLAACCREF int NULL,
        COSFCREFINFL int NULL,
        ORDFICHECMREF int NULL,
        ESENDTIME int NULL,
        veritabani_id VARCHAR(37) NULL,
        satis_fatura_id INT NULL,
        createdAt datetime NOT NULL DEFAULT GETDATE()
    );
    PRINT 'LogoInvoice tablosu başarıyla oluşturuldu';
END
ELSE
BEGIN
    PRINT 'LogoInvoice tablosu zaten mevcut';
END;

-- Table b: LogoStfiche (Similar to LG_{FFF}_{DD}_STFICHE)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LogoStfiche' and xtype='U')
BEGIN
    CREATE TABLE LogoStfiche (
        LOGICALREF int IDENTITY(1,1) NOT NULL PRIMARY KEY,
        GRPCODE smallint NULL,
        TRCODE smallint NULL,
        IOCODE smallint NULL,
        FICHENO varchar(17) NULL,
        DATE_ datetime NULL,
        FTIME int NULL,
        DOCODE varchar(33) NULL,
        INVNO varchar(17) NULL,
        SPECODE varchar(11) NULL,
        CYPHCODE varchar(11) NULL,
        INVOICEREF int NULL,
        CLIENTREF int NULL,
        RECVREF int NULL,
        ACCOUNTREF int NULL,
        CENTERREF int NULL,
        PRODORDERREF int NULL,
        PORDERFICHENO varchar(17) NULL,
        SOURCETYPE smallint NULL,
        SOURCEINDEX smallint NULL,
        SOURCEWSREF int NULL,
        SOURCEPOLNREF int NULL,
        SOURCECOSTGRP smallint NULL,
        DESTTYPE smallint NULL,
        DESTINDEX smallint NULL,
        DESTWSREF int NULL,
        DESTPOLNREF int NULL,
        DESTCOSTGRP smallint NULL,
        FACTORYNR smallint NULL,
        BRANCH smallint NULL,
        DEPARTMENT smallint NULL,
        COMPBRANCH smallint NULL,
        COMPDEPARTMENT smallint NULL,
        COMPFACTORY smallint NULL,
        PRODSTAT smallint NULL,
        DEVIR smallint NULL,
        CANCELLED smallint NULL,
        BILLED smallint NULL,
        ACCOUNTED smallint NULL,
        UPDCURR smallint NULL,
        INUSE smallint NULL,
        INVKIND smallint NULL,
        ADDDISCOUNTS float NULL,
        TOTALDISCOUNTS float NULL,
        TOTALDISCOUNTED float NULL,
        ADDEXPENSES float NULL,
        TOTALEXPENSES float NULL,
        TOTALDEPOZITO float NULL,
        TOTALPROMOTIONS float NULL,
        TOTALVAT float NULL,
        GROSSTOTAL float NULL,
        NETTOTAL float NULL,
        GENEXP1 varchar(51) NULL,
        GENEXP2 varchar(51) NULL,
        GENEXP3 varchar(51) NULL,
        GENEXP4 varchar(51) NULL,
        GENEXP5 varchar(51) NULL,
        GENEXP6 varchar(51) NULL,
        REPORTRATE float NULL,
        REPORTNET float NULL,
        EXTENREF int NULL,
        PAYDEFREF int NULL,
        PRINTCNT smallint NULL,
        FICHECNT smallint NULL,
        ACCFICHEREF int NULL,
        CAPIBLOCK_CREATEDBY smallint NULL,
        CAPIBLOCK_CREADEDDATE datetime NULL,
        CAPIBLOCK_CREATEDHOUR smallint NULL,
        CAPIBLOCK_CREATEDMIN smallint NULL,
        CAPIBLOCK_CREATEDSEC smallint NULL,
        CAPIBLOCK_MODIFIEDBY smallint NULL,
        CAPIBLOCK_MODIFIEDDATE datetime NULL,
        CAPIBLOCK_MODIFIEDHOUR smallint NULL,
        CAPIBLOCK_MODIFIEDMIN smallint NULL,
        CAPIBLOCK_MODIFIEDSEC smallint NULL,
        SALESMANREF int NULL,
        CANCELLEDACC smallint NULL,
        SHPTYPCOD varchar(13) NULL,
        SHPAGNCOD varchar(13) NULL,
        TRACKNR varchar(65) NULL,
        GENEXCTYP smallint NULL,
        LINEEXCTYP smallint NULL,
        TRADINGGRP varchar(17) NULL,
        TEXTINC smallint NULL,
        SITEID smallint NULL,
        RECSTATUS smallint NULL,
        ORGLOGICREF int NULL,
        WFSTATUS int NULL,
        SHIPINFOREF int NULL,
        DISTORDERREF int NULL,
        SENDCNT smallint NULL,
        DLVCLIENT smallint NULL,
        DOCTRACKINGNR varchar(21) NULL,
        ADDTAXCALC smallint NULL,
        TOTALADDTAX float NULL,
        UGIRTRACKINGNO varchar(17) NULL,
        QPRODFCREF int NULL,
        VAACCREF int NULL,
        VACENTERREF int NULL,
        ORGLOGOID varchar(25) NULL,
        FROMEXIM smallint NULL,
        FRGTYPCOD varchar(13) NULL,
        TRCURR smallint NULL,
        TRRATE float NULL,
        TRNET float NULL,
        EXIMWHFCREF int NULL,
        EXIMFCTYPE smallint NULL,
        MAINSTFCREF int NULL,
        FROMORDWITHPAY smallint NULL,
        PROJECTREF int NULL,
        WFLOWCRDREF int NULL,
        STATUS smallint NULL,
        UPDTRCURR smallint NULL,
        TOTALEXADDTAX float NULL,
        AFFECTCOLLATRL smallint NULL,
        DEDUCTIONPART1 smallint NULL,
        DEDUCTIONPART2 smallint NULL,
        GRPFIRMTRANS smallint NULL,
        AFFECTRISK smallint NULL,
        DISPSTATUS smallint NULL,
        APPROVE smallint NULL,
        APPROVEDATE datetime NULL,
        CANTCREDEDUCT smallint NULL,
        SHIPDATE datetime NULL,
        SHIPTIME int NULL,
        ENTRUSTDEVIR smallint NULL,
        RELTRANSFCREF int NULL,
        FROMTRANSFER smallint NULL,
        GUID varchar(37) NULL,
        GLOBALID varchar(51) NULL,
        COMPSTFCREF int NULL,
        COMPINVREF int NULL,
        TOTALSERVICES float NULL,
        CAMPAIGNCODE varchar(25) NULL,
        OFFERREF int NULL,
        EINVOICETYP smallint NULL,
        EINVOICE smallint NULL,
        NOCALCULATE smallint NULL,
        PRODORDERTYP smallint NULL,
        QPRODFCTYP smallint NULL,
        PRINTDATE datetime NULL,
        PRDORDSLPLNRESERVE smallint NULL,
        CONTROLINFO smallint NULL,
        EDESPATCH smallint NULL,
        DOCDATE datetime NULL,
        DOCTIME int NULL,
        EDESPSTATUS smallint NULL,
        PROFILEID smallint NULL,
        DELIVERYCODE varchar(11) NULL,
        DESTSTATUS smallint NULL,
        CANCELEXP varchar(251) NULL,
        UNDOEXP varchar(251) NULL,
        CANCELDATE datetime NULL,
        CREATEWHERE smallint NULL,
        PUBLICBNACCREF int NULL,
        ACCEPTEINVPUBLIC smallint NULL,
        VATEXCEPTCODE varchar(11) NULL,
        VATEXCEPTREASON varchar(201) NULL,
        ATAXEXCEPTCODE varchar(11) NULL,
        ATAXEXCEPTREASON varchar(201) NULL,
        TAXFREECHX smallint NULL,
        MNTORDERFREF int NULL,
        PRINTEDDESPFCNO varchar(17) NULL,
        OKCFICHE smallint NULL,
        ESENDDATE datetime NULL,
        NOTIFYCRDREF int NULL,
        CANCELLEDINVREF1 int NULL,
        CANCELLEDINVREF2 int NULL,
        CANCELLEDINVREF3 int NULL,
        CANCELLEDINVREF4 int NULL,
        FROMINTEGTYPE smallint NULL,
        FROMINTEGREF int NULL,
        EPRINTCNT smallint NULL,
        CLNOTREFLACNTRREF int NULL,
        CLNOTREFLAACCREF int NULL,
        FORENTRUST smallint NULL,
        PAYERCRKEY varchar(21) NULL,
        PAYERCRPROVIDER varchar(101) NULL,
        ORDFICHECMREF int NULL,
        ESENDTIME int NULL,
        veritabani_id VARCHAR(37) NULL,
        satis_fatura_id INT NULL,
        createdAt datetime NOT NULL DEFAULT GETDATE()
    );
    PRINT 'LogoStfiche tablosu başarıyla oluşturuldu';
END
ELSE
BEGIN
    PRINT 'LogoStfiche tablosu zaten mevcut';
END;

-- Table c: LogoStline (Similar to LG_{FFF}_{DD}_STLINE)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LogoStline' and xtype='U')
BEGIN
    CREATE TABLE LogoStline (
        LOGICALREF int IDENTITY(1,1) NOT NULL PRIMARY KEY,
        STOCKREF int NULL,
        LINETYPE smallint NULL,
        PREVLINEREF int NULL,
        PREVLINENO smallint NULL,
        DETLINE smallint NULL,
        TRCODE smallint NULL,
        DATE_ datetime NULL,
        FTIME int NULL,
        GLOBTRANS smallint NULL,
        CALCTYPE smallint NULL,
        PRODORDERREF int NULL,
        SOURCETYPE smallint NULL,
        SOURCEINDEX smallint NULL,
        SOURCECOSTGRP smallint NULL,
        SOURCEWSREF int NULL,
        SOURCEPOLNREF int NULL,
        DESTTYPE smallint NULL,
        DESTINDEX smallint NULL,
        DESTCOSTGRP smallint NULL,
        DESTWSREF int NULL,
        DESTPOLNREF int NULL,
        FACTORYNR smallint NULL,
        IOCODE smallint NULL,
        STFICHEREF int NULL,
        STFICHELNNO smallint NULL,
        INVOICEREF int NULL,
        INVOICELNNO smallint NULL,
        CLIENTREF int NULL,
        ORDTRANSREF int NULL,
        ORDFICHEREF int NULL,
        CENTERREF int NULL,
        ACCOUNTREF int NULL,
        VATACCREF int NULL,
        VATCENTERREF int NULL,
        PRACCREF int NULL,
        PRCENTERREF int NULL,
        PRVATACCREF int NULL,
        PRVATCENREF int NULL,
        PROMREF int NULL,
        PAYDEFREF int NULL,
        SPECODE varchar(17) NULL,
        DELVRYCODE varchar(11) NULL,
        AMOUNT float NULL,
        PRICE float NULL,
        TOTAL float NULL,
        PRCURR smallint NULL,
        PRPRICE float NULL,
        TRCURR smallint NULL,
        TRRATE float NULL,
        REPORTRATE float NULL,
        DISTCOST float NULL,
        DISTDISC float NULL,
        DISTEXP float NULL,
        DISTPROM float NULL,
        DISCPER float NULL,
        LINEEXP varchar(251) NULL,
        UOMREF int NULL,
        USREF int NULL,
        UINFO1 float NULL,
        UINFO2 float NULL,
        UINFO3 float NULL,
        UINFO4 float NULL,
        UINFO5 float NULL,
        UINFO6 float NULL,
        UINFO7 float NULL,
        UINFO8 float NULL,
        PLNAMOUNT float NULL,
        VATINC smallint NULL,
        VAT float NULL,
        VATAMNT float NULL,
        VATMATRAH float NULL,
        BILLEDITEM int NULL,
        BILLED smallint NULL,
        CPSTFLAG smallint NULL,
        RETCOSTTYPE smallint NULL,
        SOURCELINK int NULL,
        RETCOST float NULL,
        RETCOSTCURR float NULL,
        OUTCOST float NULL,
        OUTCOSTCURR float NULL,
        RETAMOUNT float NULL,
        FAREGREF int NULL,
        FAATTRIB smallint NULL,
        CANCELLED smallint NULL,
        LINENET float NULL,
        DISTADDEXP float NULL,
        FADACCREF int NULL,
        FADCENTERREF int NULL,
        FARACCREF int NULL,
        FARCENTERREF int NULL,
        DIFFPRICE float NULL,
        DIFFPRCOST float NULL,
        DECPRDIFF smallint NULL,
        LPRODSTAT smallint NULL,
        PRDEXPTOTAL float NULL,
        DIFFREPPRICE float NULL,
        DIFFPRCRCOST float NULL,
        SALESMANREF int NULL,
        FAPLACCREF int NULL,
        FAPLCENTERREF int NULL,
        OUTPUTIDCODE varchar(25) NULL,
        DREF int NULL,
        COSTRATE float NULL,
        XPRICEUPD smallint NULL,
        XPRICE float NULL,
        XREPRATE float NULL,
        DISTCOEF float NULL,
        TRANSQCOK smallint NULL,
        SITEID smallint NULL,
        RECSTATUS smallint NULL,
        ORGLOGICREF int NULL,
        WFSTATUS int NULL,
        POLINEREF int NULL,
        PLNSTTRANSREF int NULL,
        NETDISCFLAG smallint NULL,
        NETDISCPERC float NULL,
        NETDISCAMNT float NULL,
        VATCALCDIFF float NULL,
        CONDITIONREF int NULL,
        DISTORDERREF int NULL,
        DISTORDLINEREF int NULL,
        CAMPAIGNREFS1 int NULL,
        CAMPAIGNREFS2 int NULL,
        CAMPAIGNREFS3 int NULL,
        CAMPAIGNREFS4 int NULL,
        CAMPAIGNREFS5 int NULL,
        POINTCAMPREF int NULL,
        CAMPPOINT float NULL,
        PROMCLASITEMREF int NULL,
        CMPGLINEREF int NULL,
        PLNSTTRANSPERNR int NULL,
        PORDCLSPLNAMNT float NULL,
        VENDCOMM float NULL,
        PREVIOUSOUTCOST float NULL,
        COSTOFSALEACCREF int NULL,
        PURCHACCREF int NULL,
        COSTOFSALECNTREF int NULL,
        PURCHCENTREF int NULL,
        PREVOUTCOSTCURR float NULL,
        ABVATAMOUNT float NULL,
        ABVATSTATUS int NULL,
        PRRATE float NULL,
        ADDTAXRATE float NULL,
        ADDTAXCONVFACT float NULL,
        ADDTAXAMOUNT float NULL,
        ADDTAXPRCOST float NULL,
        ADDTAXRETCOST float NULL,
        ADDTAXRETCOSTCURR float NULL,
        GROSSUINFO1 float NULL,
        GROSSUINFO2 float NULL,
        ADDTAXPRCOSTCURR float NULL,
        ADDTAXACCREF int NULL,
        ADDTAXCENTERREF int NULL,
        ADDTAXAMNTISUPD smallint NULL,
        INFIDX float NULL,
        ADDTAXCOSACCREF int NULL,
        ADDTAXCOSCNTREF int NULL,
        PREVIOUSATAXPRCOST float NULL,
        PREVATAXPRCOSTCURR float NULL,
        PRDORDTOTCOEF float NULL,
        DEMPEGGEDAMNT float NULL,
        STDUNITCOST float NULL,
        STDRPUNITCOST float NULL,
        COSTDIFFACCREF int NULL,
        COSTDIFFCENREF int NULL,
        TEXTINC smallint NULL,
        ADDTAXDISCAMOUNT float NULL,
        ORGLOGOID varchar(25) NULL,
        EXIMFICHENO varchar(31) NULL,
        EXIMFCTYPE smallint NULL,
        TRANSEXPLINE smallint NULL,
        INSEXPLINE smallint NULL,
        EXIMWHFCREF int NULL,
        EXIMWHLNREF int NULL,
        EXIMFILEREF int NULL,
        EXIMPROCNR smallint NULL,
        EISRVDSTTYP smallint NULL,
        MAINSTLNREF int NULL,
        MADEOFSHRED smallint NULL,
        FROMORDWITHPAY smallint NULL,
        PROJECTREF int NULL,
        STATUS smallint NULL,
        DORESERVE smallint NULL,
        POINTCAMPREFS1 int NULL,
        POINTCAMPREFS2 int NULL,
        POINTCAMPREFS3 int NULL,
        POINTCAMPREFS4 int NULL,
        CAMPPOINTS1 float NULL,
        CAMPPOINTS2 float NULL,
        CAMPPOINTS3 float NULL,
        CAMPPOINTS4 float NULL,
        CMPGLINEREFS1 int NULL,
        CMPGLINEREFS2 int NULL,
        CMPGLINEREFS3 int NULL,
        CMPGLINEREFS4 int NULL,
        PRCLISTREF int NULL,
        PORDSYMOUTLN smallint NULL,
        MONTH_ smallint NULL,
        YEAR_ smallint NULL,
        EXADDTAXRATE float NULL,
        EXADDTAXCONVF float NULL,
        EXADDTAXAREF int NULL,
        EXADDTAXCREF int NULL,
        OTHRADDTAXAREF int NULL,
        OTHRADDTAXCREF int NULL,
        EXADDTAXAMNT float NULL,
        AFFECTCOLLATRL smallint NULL,
        ALTPROMFLAG smallint NULL,
        EIDISTFLNNR smallint NULL,
        EXIMTYPE smallint NULL,
        VARIANTREF int NULL,
        CANDEDUCT smallint NULL,
        OUTREMAMNT float NULL,
        OUTREMCOST float NULL,
        OUTREMCOSTCURR float NULL,
        REFLVATACCREF int NULL,
        REFLVATOTHACCREF int NULL,
        PARENTLNREF int NULL,
        AFFECTRISK smallint NULL,
        INEFFECTIVECOST smallint NULL,
        ADDTAXVATMATRAH float NULL,
        REFLACCREF int NULL,
        REFLOTHACCREF int NULL,
        CAMPPAYDEFREF int NULL,
        FAREGBINDDATE datetime NULL,
        RELTRANSLNREF int NULL,
        FROMTRANSFER smallint NULL,
        COSTDISTPRICE float NULL,
        COSTDISTREPPRICE float NULL,
        DIFFPRICEUFRS float NULL,
        DIFFREPPRICEUFRS float NULL,
        OUTCOSTUFRS float NULL,
        OUTCOSTCURRUFRS float NULL,
        DIFFPRCOSTUFRS float NULL,
        DIFFPRCRCOSTUFRS float NULL,
        RETCOSTUFRS float NULL,
        RETCOSTCURRUFRS float NULL,
        OUTREMCOSTUFRS float NULL,
        OUTREMCOSTCURRUFRS float NULL,
        INFIDXUFRS float NULL,
        ADJPRICEUFRS float NULL,
        ADJREPPRICEUFRS float NULL,
        ADJPRCOSTUFRS float NULL,
        ADJPRCRCOSTUFRS float NULL,
        COSTDISTPRICEUFRS float NULL,
        COSTDISTREPPRICEUFRS float NULL,
        PURCHACCREFUFRS int NULL,
        PURCHCENTREFUFRS int NULL,
        COSACCREFUFRS int NULL,
        COSCNTREFUFRS int NULL,
        PROUTCOSTUFRSDIFF float NULL,
        PROUTCOSTCRUFRSDIFF float NULL,
        UNDERDEDUCTLIMIT smallint NULL,
        GLOBALID varchar(51) NULL,
        DEDUCTIONPART1 smallint NULL,
        DEDUCTIONPART2 smallint NULL,
        GUID varchar(37) NULL,
        SPECODE2 varchar(41) NULL,
        OFFERREF int NULL,
        OFFTRANSREF int NULL,
        VATEXCEPTREASON varchar(201) NULL,
        PLNDEFSERILOTNO varchar(101) NULL,
        PLNUNRSRVAMOUNT float NULL,
        PORDCLSPLNUNRSRVAMNT float NULL,
        LPRODRSRVSTAT smallint NULL,
        FALINKTYPE smallint NULL,
        DEDUCTCODE varchar(11) NULL,
        UPDTHISLINE smallint NULL,
        VATEXCEPTCODE varchar(11) NULL,
        PORDERFICHENO varchar(17) NULL,
        QPRODFCREF int NULL,
        RELTRANSFCREF int NULL,
        ATAXEXCEPTREASON varchar(201) NULL,
        ATAXEXCEPTCODE varchar(11) NULL,
        PRODORDERTYP smallint NULL,
        SUBCONTORDERREF int NULL,
        QPRODFCTYP smallint NULL,
        PRDORDSLPLNRESERVE smallint NULL,
        INFDATE datetime NULL,
        DESTSTATUS smallint NULL,
        REGTYPREF int NULL,
        FAPROFITACCREF int NULL,
        FAPROFITCENTREF int NULL,
        FALOSSACCREF int NULL,
        FALOSSCENTREF int NULL,
        CPACODE varchar(25) NULL,
        GTIPCODE varchar(25) NULL,
        PUBLICCOUNTRYREF int NULL,
        QPRODITEMTYPE smallint NULL,
        FUTMONTHCNT smallint NULL,
        FUTMONTHBEGDATE int NULL,
        QCTRANSFERREF int NULL,
        QCTRANSFERAMNT float NULL,
        FUTMONTHENDDATE datetime NULL,
        KKEGACCREF int NULL,
        KKEGCENTREF int NULL,
        MNTORDERFREF int NULL,
        FAKKEGAMOUNT float NULL,
        MIDDLEMANEXPTYP smallint NULL,
        EXPRACCREF int NULL,
        EXPRCNTRREF int NULL,
        KKEGVATACCREF int NULL,
        KKEGVATCENTREF int NULL,
        MARKINGTAGNO varchar(21) NULL,
        OWNER varchar(201) NULL,
        TCKTAXNR varchar(16) NULL,
        FUTMONTHBEGDATE_ datetime NULL,
        ADDTAXVATACCREF int NULL,
        ADDTAXVATCENREF int NULL,
        EXPDAYS smallint NULL,
        CANCELLEDINVREF1 int NULL,
        CANCELLEDINVREF2 int NULL,
        CANCELLEDINVREF3 int NULL,
        CANCELLEDINVREF4 int NULL,
        FROMINTEGTYPE smallint NULL,
        FROMINTEGREF int NULL,
        QCTRANSFERREF2 int NULL,
        QCTRANSFERAMNT2 float NULL,
        EISRVDSTADDTAXINC smallint NULL,
        TAXFREEACCREF int NULL,
        TAXFREECNTRREF int NULL,
        ADDTAXEFFECTKDV smallint NULL,
        ADDTAXINLINENET smallint NULL,
        ITMDISC smallint NULL,
        ADDTAXREF int NULL,
        COSCNTREFINFL int NULL,
        PROUTCOSTINFLDIFF float NULL,
        PROUTCOSTCRINFLDIFF float NULL,
        COSACCREFINFL int NULL,
        ORDFICHECMREF int NULL,
        PURCHACCREFINFL int NULL,
        PURCHCENTREFINFL int NULL,
        DIIBLINECODE varchar(12) NULL,
        RETSOURCELINK int NULL,
        ORGPRICE float NULL,
        veritabani_id VARCHAR(37) NULL,
        satis_fatura_id INT NULL,
        satis_fatura_satir_id INT NULL,
        createdAt datetime NOT NULL DEFAULT GETDATE()
    );
    PRINT 'LogoStline tablosu başarıyla oluşturuldu';
END
ELSE
BEGIN
    PRINT 'LogoStline tablosu zaten mevcut';
END;

-- Check if tables were created successfully
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LogoInvoice' and xtype='U')
    THROW 50010, 'LogoInvoice tablosu düzgün oluşturulmadı', 1;
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LogoStfiche' and xtype='U')
    THROW 50011, 'LogoStfiche tablosu düzgün oluşturulmadı', 1;
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LogoStline' and xtype='U')
    THROW 50012, 'LogoStline tablosu düzgün oluşturulmadı', 1;

PRINT 'Logo satış faturası tabloları başarıyla oluşturuldu';
`
