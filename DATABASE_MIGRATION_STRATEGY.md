# VNS Mizan Database Migration Strategy

## Genel Yaklaşım

VNS Mizan projesi **test ortamında** oldu<PERSON><PERSON> için, production veritabanı uyumluluğu endişesi olmadan **en basit ve doğrudan yaklaşımı** kullanıyoruz.

## Migration Stratejisi: <PERSON><PERSON><PERSON>an Tablo Oluşturma

### Neden Migration Dosyaları Kullanmıyoruz?

1. **Test Ortamı:** Henüz production veritabanımız yok
2. **Esneklik:** Tablo yapılarını kolayca değiştirebiliyoruz
3. **Basitlik:** Karmaşık migration mantığına gerek yok
4. **Hızlı Geliştirme:** Değişiklikleri anında uygulayabiliyoruz

### Yaklaşımımız

**❌ YAPMAYIN:**
```typescript
// Ayrı migration dosyası oluşturmayın
export const logoLoggingMigration = `
  ALTER TABLE CariHesaplar ADD logo_rest_data VARCHAR(MAX);
`
```

**✅ YAPIN:**
```typescript
// Ana tablo tanımında doğrudan ekleyin
CREATE TABLE CariHesaplar (
    id INT PRIMARY KEY IDENTITY(1,1),
    kodu VARCHAR(17),
    // ... diğer alanlar
    logo_rest_data VARCHAR(MAX) NULL,
    logo_sql_data VARCHAR(MAX) NULL,
    createdAt DATETIME2 NOT NULL DEFAULT GETDATE()
);
```

## Logo Loglama Sütunları Standardı

### Zorunlu Sütunlar
Tüm iş modülü tablolarında şu sütunlar **mutlaka** bulunmalıdır:

```sql
logo_rest_data VARCHAR(MAX) NULL,
logo_sql_data VARCHAR(MAX) NULL
```

### Hangi Tablolarda Gerekli?

#### Ana İş Modülü Tabloları
- ✅ `CariHesaplar`
- ✅ `SatisFaturalari`
- ✅ `SatisFaturalariSatirlari`
- ✅ `SatinalmaFaturalari`
- ✅ `SatinalmaFaturalariSatirlari`
- ✅ `Stoklar` (gelecekte)
- ✅ `Reyonlar` (gelecekte)
- ✅ `AltGruplar` (gelecekte)
- ✅ `AnaGruplar` (gelecekte)
- ✅ `Kategoriler` (gelecekte)
- ✅ `Markalar` (gelecekte)

#### Sistem Tabloları (Loglama Gerekmez)
- ❌ `users`
- ❌ `sessions`
- ❌ `LogoCariHesaplar` (zaten Logo verisi)

## Tablo Oluşturma Şablonu

### Yeni İş Modülü Tablosu Oluştururken

```sql
CREATE TABLE YeniModulTablosu (
    id INT PRIMARY KEY IDENTITY(1,1),
    
    -- İş alanları
    alan1 VARCHAR(50),
    alan2 INT,
    alan3 DATE,
    
    -- Sistem alanları
    veritabani_id VARCHAR(37),
    error NVARCHAR(MAX),
    
    -- Logo loglama alanları (ZORUNLU)
    logo_rest_data VARCHAR(MAX) NULL,
    logo_sql_data VARCHAR(MAX) NULL,
    
    -- Zaman damgaları
    createdAt DATETIME2 NOT NULL DEFAULT GETDATE(),
    updatedAt DATETIME2 NOT NULL DEFAULT GETDATE()
);
```

### Mevcut Tablo Güncellerken

```sql
-- Mevcut tablo tanımını bulun (örn: src/shared/database/queries/cariler.ts)
-- CREATE TABLE bölümünde logo loglama sütunlarını ekleyin

CREATE TABLE CariHesaplar (
    -- Mevcut alanlar...
    logoRef INT,
    error NVARCHAR(MAX),
    
    -- YENİ: Logo loglama sütunları
    logo_rest_data VARCHAR(MAX) NULL,
    logo_sql_data VARCHAR(MAX) NULL,
    
    -- Zaman damgaları
    createdAt DATETIME2 NOT NULL DEFAULT GETDATE(),
    updatedAt DATETIME2 NOT NULL DEFAULT GETDATE()
);
```

## Dosya Konumları

### Ana Tablo Tanım Dosyaları
```
src/shared/database/queries/
├── cariler.ts              ✅ Güncellendi
├── satis-faturalari.ts     ✅ Güncellendi  
├── satinalma-faturalari.ts ✅ Güncellendi
├── stoklar.ts              🔄 Gelecekte güncellenecek
├── reyonlar.ts             🔄 Gelecekte güncellenecek
└── ...
```

### DbInitService Konfigürasyonu
```typescript
// src/shared/services/db-init-service.ts
const businessTableInitializers = [
  {
    connectionName: 'db',
    query: carilerQuery,        // Logo loglama sütunları dahil
    isRequired: true,
    migrationName: 'Cari hesap tabloları',
  },
  // Ayrı migration dosyası EKLEMEYIN
]
```

## AI Agent Rehberi

### ✅ Doğru Yaklaşım
1. **Mevcut tablo tanım dosyasını bulun** (`src/shared/database/queries/`)
2. **CREATE TABLE** bölümünde logo loglama sütunlarını ekleyin
3. **Sistem testi** yapın (veritabanı başlatma)
4. **Service layer'ı** güncelleyin (loglama fonksiyonları)

### ❌ Yanlış Yaklaşım
1. ~~Ayrı migration dosyası oluşturmayın~~
2. ~~ALTER TABLE komutları kullanmayın~~
3. ~~DbInitService'e yeni migration eklemeyin~~
4. ~~Backward compatibility endişesi yapmayın~~

## Loglama Sütunlarının Kullanımı

### logo_rest_data
```json
{
  "endpoint": "/api/v1/Arps",
  "method": "POST",
  "timestamp": "2024-01-15T10:30:00Z",
  "payload": { /* Logo REST API'ye gönderilen veri */ },
  "response_status": 200,
  "response_data": { /* Logo'dan gelen yanıt */ }
}
```

### logo_sql_data
```json
{
  "LG_005_CLCARD": {
    "LOGICALREF": 98765,
    "CODE": "CARI001",
    "operation": "INSERT",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## Test ve Doğrulama

### Sistem Başlatma Testi
```bash
cd vns-mizan-rest-service
pnpm dev
```

**Beklenen Çıktı:**
```
✔ db bağlantısı için Cari hesap tabloları tamamlandı
✔ db bağlantısı için Satış fatura tabloları tamamlandı
✔ db bağlantısı için Satınalma fatura tabloları tamamlandı
```

### Veritabanı Kontrolü
```sql
-- Sütunların varlığını kontrol edin
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'CariHesaplar' 
AND COLUMN_NAME IN ('logo_rest_data', 'logo_sql_data');
```

## Gelecek Geliştirmeler

### Yeni Modül Eklerken
1. **Tablo tanımı** oluştururken logo loglama sütunlarını dahil edin
2. **Service layer** fonksiyonlarında loglama desteği ekleyin
3. **Utility functions** kullanarak JSON loglama verisi oluşturun

### Production'a Geçerken
Production ortamına geçtiğimizde:
1. **Mevcut yaklaşımı koruyun** (çalışıyor)
2. **Backup stratejisi** geliştirin
3. **Migration tracking** sistemi ekleyin (gerekirse)

---

**Önemli:** Bu strateji test ortamı için optimize edilmiştir. Production ortamında farklı yaklaşımlar gerekebilir, ancak şu an için en pratik ve etkili yöntemdir.
