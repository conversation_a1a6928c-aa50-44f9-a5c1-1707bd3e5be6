/**
 * SQL queries for 'cari_hesaplar' database initialization and operations
 */
export const query = `
    -- 'CariHesaplar' tablosu yoksa oluştur
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CariHesaplar' and xtype='U')
    BEGIN
        CREATE TABLE CariHesaplar (
            id INT PRIMARY KEY IDENTITY(1,1),
            kodu VARCHAR(17),
            vkVeyaTckNo VARCHAR(11),
            unvan VARCHAR(200),
            ad VARCHAR(50),
            soyad VARCHAR(50),
            adres VARCHAR(400),
            il VARCHAR(50),
            ilce VARCHAR(50),
            ulke VARCHAR(50),
            ulkeKodu VARCHAR(10),
            email VARCHAR(100),
            postaKodu VARCHAR(6),
            ozelKod VARCHAR(10),
            veritabani_id VARCHAR(37),
            logoRef INT,
            error NVARCHAR(MAX),
            createdAt DATETIME2 NOT NULL DEFAULT GETDATE(),
            updatedAt DATETIME2 NOT NULL DEFAULT GETDATE()
        );

        CREATE INDEX IX_CariHesaplar_vkVeyaTckNo ON CariHesaplar(vkVeyaTckNo);
        CREATE INDEX IX_CariHesaplar_logoRef ON CariHesaplar(logoRef);
        
        PRINT 'CariHesaplar tablosu başarıyla oluşturuldu';
    END
    ELSE
    BEGIN
        PRINT 'CariHesaplar tablosu zaten mevcut';
    END;
    
    -- 'LogoCariHesaplar' tablosu yoksa oluştur
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LogoCariHesaplar' and xtype='U')
    BEGIN
        CREATE TABLE LogoCariHesaplar (
            id INT PRIMARY KEY IDENTITY(1,1),
            cariHesapId INT,
            code VARCHAR(17),
            title VARCHAR(200),
            name VARCHAR(50),
            surname VARCHAR(50),
            address1 VARCHAR(200),
            address2 VARCHAR(200),
            town VARCHAR(50),
            city VARCHAR(50),
            country VARCHAR(50),
            countryCode VARCHAR(10),
            postalCode VARCHAR(6),
            auxilCode VARCHAR(50),
            email VARCHAR(100),
            taxId VARCHAR(10),
            tcId VARCHAR(11),
            accountType SMALLINT,
            acceptEInv SMALLINT,
            postLabel VARCHAR(100),
            senderLabel VARCHAR(100),
            veritabani_id VARCHAR(37),
            profileId VARCHAR(10),
            insteadOfDispatch SMALLINT,
            createdAt DATETIME2 NOT NULL DEFAULT GETDATE(),
            FOREIGN KEY (cariHesapId) REFERENCES CariHesaplar(id)
        );

        CREATE INDEX IX_LogoCariHesaplar_cariHesapId ON LogoCariHesaplar(cariHesapId);
        CREATE INDEX IX_LogoCariHesaplar_code ON LogoCariHesaplar(code);
        
        PRINT 'LogoCariHesaplar tablosu başarıyla oluşturuldu';
    END
    ELSE
    BEGIN
        PRINT 'LogoCariHesaplar tablosu zaten mevcut';
    END;

    -- Tabloların oluşturulup oluşturulmadığını kontrol et
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CariHesaplar' and xtype='U')
        THROW 50008, 'CariHesaplar tablosu düzgün oluşturulmadı', 1;
    
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LogoCariHesaplar' and xtype='U')
        THROW 50009, 'LogoCariHesaplar tablosu düzgün oluşturulmadı', 1;
`
