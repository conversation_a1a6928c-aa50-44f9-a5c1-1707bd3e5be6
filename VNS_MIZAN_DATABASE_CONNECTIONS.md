# VNS Mizan Database Connection Architecture

## Genel Bakış

VNS Mizan sistemi iki farklı türde veritabanı bağlantısı kullanır:
1. **Ana Uygulama Veritabanı** - VnsMizanEntegrasyonDb (bizim verilerimiz)
2. **Logo ERP Veritabanları** - Müşterilerin Logo ERP veritabanları (çoklu bağlantı)

## Ana Uygulama Veritabanı Bağlantısı

### Bağlantı Kurma
```typescript
const dbConnection = await DbService.getConnection('db')
```

### Kullanım Alanları
- **İş Modülü Tabloları:** CariHesaplar, SatisFaturalari, SatinalmaFaturalari
- **Sistem Tabloları:** users, sessions
- **Loglama Verileri:** logo_rest_data, logo_sql_data sütunları
- **Tracking Tabloları:** LogoCariHesaplar, LogoSatisFaturalari

### Konfigürasyon
```json
{
  "database_connections": {
    "db": {
      "server": "localhost",
      "database": "VnsMizanEntegrasyonDb",
      "user": "sa",
      "password": "password",
      "port": 1433,
      "encrypt": false,
      "trust_server_certificate": true
    }
  }
}
```

### Connection Pool Özellikleri
- **Pool Size:** Otomatik yönetim
- **Timeout:** 30 saniye
- **Retry Logic:** Otomatik yeniden bağlantı
- **Health Check:** Düzenli bağlantı kontrolü

## Logo ERP Veritabanı Bağlantıları (Multi-Tenant)

### Bağlantı Kurma
```typescript
const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
```

### Multi-Tenant Mimari
VNS Mizan aynı anda birden fazla Logo ERP veritabanına bağlanabilir:

```json
{
  "project_settings": {
    "logo": {
      "db_connections": [
        {
          "id": "ca108341-f2d1-44a3-b0db-9d7871917758",
          "active": true,
          "name": "Firma A - 2024 Dönemi",
          "sql": {
            "server": "LOGO-SERVER-1",
            "database": "TigerDb",
            "user": "sa",
            "password": "Logo123",
            "port": 1433
          },
          "erp": {
            "firma_numarasi": "004",
            "donem_numarasi": "01"
          }
        },
        {
          "id": "2726b8c5-75ce-461d-a771-08b07bad99b0",
          "active": true,
          "name": "Firma B - 2024 Dönemi",
          "sql": {
            "server": "LOGO-SERVER-2",
            "database": "TigerDb2",
            "user": "sa",
            "password": "Logo456"
          },
          "erp": {
            "firma_numarasi": "005",
            "donem_numarasi": "01"
          }
        }
      ]
    }
  }
}
```

### veritabani_id Parametresi

#### API İsteklerinde Kullanım
```typescript
// POST /v1/cariler
{
  "veritabani_id": "ca108341-f2d1-44a3-b0db-9d7871917758",
  "kodu": "CARI001",
  "unvan": "Müşteri Adı"
}
```

#### Sistem İçinde Routing
```typescript
export async function sendCariHesap({
  cariHesapData,
  veritabaniId  // Bu parametre ile hangi Logo ERP'ye gideceğini belirliyoruz
}: {
  cariHesapData: CariHesapRequest
  veritabaniId: string
}) {
  // 1. veritabani_id ile Logo konfigürasyonunu al
  const logoConfig = await getLogoConfigById(veritabaniId)
  
  // 2. Bu konfigürasyona göre Logo veritabanına bağlan
  const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
  
  // 3. İşlemleri bu bağlantı üzerinden yap
}
```

### Logo Tablo Adlandırma Konvansiyonu

Logo ERP'de tablo isimleri firma ve dönem numarasına göre oluşturulur:

#### Dönem Numarası Olan Tablolar
```
LG_{FIRMA_NO}_{DONEM_NO}_{TABLO_ADI}
```
**Örnekler:**
- `LG_004_01_INVOICE` - 004 firması, 01 dönemi, fatura tablosu
- `LG_005_01_STLINE` - 005 firması, 01 dönemi, fatura satır tablosu

#### Dönem Numarası Olmayan Tablolar
```
LG_{FIRMA_NO}_{TABLO_ADI}
```
**Örnekler:**
- `LG_004_CLCARD` - 004 firması, cari hesap kartları
- `LG_005_ITEMS` - 005 firması, stok kartları

### Connection Pool Stratejileri

#### Ana Uygulama Veritabanı
- **Single Pool:** Tek veritabanı için tek connection pool
- **Always Connected:** Sürekli bağlantı halinde
- **High Performance:** Yüksek performans için optimize edilmiş

#### Logo ERP Veritabanları
- **Multiple Pools:** Her Logo ERP için ayrı connection pool
- **On-Demand:** İhtiyaç halinde bağlantı kurma
- **Resource Management:** Kaynak yönetimi ile pool boyutu kontrolü

```typescript
// Her Logo ERP bağlantısı için ayrı pool
const connectionPools = new Map<string, ConnectionPool>()

async function getLogoConnectionById(veritabaniId: string) {
  if (!connectionPools.has(veritabaniId)) {
    const config = await getLogoConfigById(veritabaniId)
    const pool = new ConnectionPool(config.sql)
    await pool.connect()
    connectionPools.set(veritabaniId, pool)
  }
  
  return connectionPools.get(veritabaniId)
}
```

## Bağlantı Yönetimi Best Practices

### 1. Connection Lifecycle
```typescript
// ✅ Doğru kullanım
async function processData(veritabaniId: string) {
  const connection = await DbService.getLogoConnectionById(veritabaniId)
  const request = connection.request()
  // ... işlemler
  // Pool otomatik olarak bağlantıyı yönetir
}

// ❌ Yanlış kullanım - manuel close
async function processData(veritabaniId: string) {
  const connection = await DbService.getLogoConnectionById(veritabaniId)
  // ... işlemler
  await connection.close() // Pool'u bozar!
}
```

### 2. Error Handling
```typescript
async function safeDbOperation(veritabaniId: string) {
  try {
    const connection = await DbService.getLogoConnectionById(veritabaniId)
    // ... işlemler
  } catch (error) {
    consola.error('Veritabanı bağlantı hatası:', error)
    // Pool otomatik olarak retry yapar
    throw error
  }
}
```

### 3. Transaction Management
```typescript
async function transactionalOperation(veritabaniId: string) {
  const connection = await DbService.getLogoConnectionById(veritabaniId)
  const transaction = new sql.Transaction(connection)
  
  try {
    await transaction.begin()
    
    // ... işlemler
    
    await transaction.commit()
  } catch (error) {
    await transaction.rollback()
    throw error
  }
}
```

## Monitoring ve Debugging

### Connection Pool Monitoring
```typescript
// Pool durumunu kontrol etme
const poolInfo = {
  totalConnections: pool.totalConnections,
  idleConnections: pool.idleConnections,
  busyConnections: pool.busyConnections
}
```

### Bağlantı Sorunları Debug
```typescript
// Bağlantı test etme
async function testConnection(veritabaniId: string) {
  try {
    const connection = await DbService.getLogoConnectionById(veritabaniId)
    const result = await connection.request().query('SELECT 1 as test')
    consola.success(`Bağlantı başarılı: ${veritabaniId}`)
    return true
  } catch (error) {
    consola.error(`Bağlantı hatası: ${veritabaniId}`, error)
    return false
  }
}
```

## Güvenlik Considerations

### 1. Connection String Security
- Şifreler config.json'da encrypted olarak saklanmalı
- Production'da environment variables kullanılmalı
- Connection timeout değerleri güvenlik için ayarlanmalı

### 2. SQL Injection Prevention
```typescript
// ✅ Güvenli - Parameterized queries
const request = connection.request()
request.input('kodu', sql.VarChar, cariKodu)
await request.query('SELECT * FROM LG_004_CLCARD WHERE CODE = @kodu')

// ❌ Güvensiz - String concatenation
await request.query(`SELECT * FROM LG_004_CLCARD WHERE CODE = '${cariKodu}'`)
```

### 3. Access Control
- Her Logo ERP bağlantısı için minimum gerekli izinler
- Read-only operasyonlar için ayrı kullanıcılar
- Audit logging tüm veritabanı operasyonları için

---

Bu dokümantasyon VNS Mizan'ın veritabanı bağlantı mimarisini kapsamlı şekilde açıklamaktadır. Gelecekte çalışacak AI agent'lar bu rehberi kullanarak doğru bağlantı yönetimi yapabilirler.
