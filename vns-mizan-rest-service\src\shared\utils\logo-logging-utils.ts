/**
 * Logo Entegrasyon Loglama Yardımcı Fonksiyonları
 * 
 * Bu dosya, Logo REST API ve SQL entegrasyonu için
 * JSON formatında loglama yapan yardımcı fonksiyonları içerir.
 */

import { getLogoConfigById } from './config-utils.ts'

/**
 * Logo REST API loglama verisi için interface
 */
export interface LogoRestLogData {
  endpoint: string
  method: string
  timestamp: string
  payload: any
  response_status?: number
  response_data?: any
  error?: string
}

/**
 * Logo SQL loglama verisi için interface
 */
export interface LogoSqlLogData {
  [tableName: string]: LogoSqlTableData | LogoSqlTableData[]
}

/**
 * Logo SQL tablo verisi için interface
 */
export interface LogoSqlTableData {
  operation: 'INSERT' | 'UPDATE' | 'DELETE'
  timestamp: string
  [field: string]: any
}

/**
 * Logo REST API loglama verisi oluşturur
 * 
 * @param endpoint - Logo REST API endpoint'i
 * @param method - HTTP method (POST, PUT, DELETE)
 * @param payload - Logo'ya gönderilen veri
 * @param responseStatus - HTTP response status kodu
 * @param responseData - Logo'dan gelen yanıt
 * @param error - Hata mesajı (varsa)
 * @returns JSON string formatında loglama verisi
 */
export function createLogoRestLogData({
  endpoint,
  method,
  payload,
  responseStatus,
  responseData,
  error
}: {
  endpoint: string
  method: string
  payload: any
  responseStatus?: number
  responseData?: any
  error?: string
}): string {
  const logData: LogoRestLogData = {
    endpoint,
    method,
    timestamp: new Date().toISOString(),
    payload,
    response_status: responseStatus,
    response_data: responseData,
    error
  }

  return JSON.stringify(logData, null, 2)
}

/**
 * Logo SQL loglama verisi oluşturur
 * 
 * @param veritabaniId - Veritabanı ID'si (firma/dönem bilgisi için)
 * @param tableData - Tablo verisi (tablo adı ve veri)
 * @returns JSON string formatında loglama verisi
 */
export async function createLogoSqlLogData({
  veritabaniId,
  tableData
}: {
  veritabaniId: string
  tableData: { [tableName: string]: any | any[] }
}): Promise<string> {
  try {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const firmaNo = logoConfig.erp.firma_numarasi
    const donemNo = logoConfig.erp.donem_numarasi
    
    const logData: LogoSqlLogData = {}
    const timestamp = new Date().toISOString()

    // Her tablo için Logo tablo adı formatında organize et
    for (const [baseTableName, data] of Object.entries(tableData)) {
      const logoTableName = createLogoTableName(baseTableName, firmaNo, donemNo)
      
      if (Array.isArray(data)) {
        // Çoklu kayıt durumu (örn: fatura satırları)
        logData[logoTableName] = data.map(item => ({
          ...item,
          operation: item.operation || 'INSERT',
          timestamp
        }))
      } else {
        // Tekli kayıt durumu (örn: fatura başlığı)
        logData[logoTableName] = {
          ...data,
          operation: data.operation || 'INSERT',
          timestamp
        }
      }
    }

    return JSON.stringify(logData, null, 2)
  } catch (error) {
    // Hata durumunda basit format döndür
    const errorLogData = {
      error: `Logo SQL log data oluşturulurken hata: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
      timestamp: new Date().toISOString(),
      original_data: tableData
    }
    return JSON.stringify(errorLogData, null, 2)
  }
}

/**
 * Logo tablo adı oluşturur
 * 
 * @param baseTableName - Temel tablo adı (örn: INVOICE, CLCARD, STLINE)
 * @param firmaNo - Firma numarası
 * @param donemNo - Dönem numarası (opsiyonel, bazı tablolarda yok)
 * @returns Logo formatında tablo adı
 */
export function createLogoTableName(baseTableName: string, firmaNo: string, donemNo?: string): string {
  // Dönem numarası gerektiren tablolar
  const donemliTablolar = [
    'INVOICE', 'STFICHE', 'STLINE', 'ORFICHE', 'ORLINE', 
    'PURCHINV', 'PURCHLINE', 'PAYPLANS', 'CLFICHE', 'CLLINE'
  ]
  
  const upperTableName = baseTableName.toUpperCase()
  
  if (donemNo && donemliTablolar.includes(upperTableName)) {
    return `LG_${firmaNo}_${donemNo}_${upperTableName}`
  } else {
    return `LG_${firmaNo}_${upperTableName}`
  }
}

/**
 * Cari hesap için Logo SQL log data oluşturur
 */
export async function createCariHesapSqlLogData({
  veritabaniId,
  cariData,
  operation = 'INSERT'
}: {
  veritabaniId: string
  cariData: any
  operation?: 'INSERT' | 'UPDATE' | 'DELETE'
}): Promise<string> {
  return createLogoSqlLogData({
    veritabaniId,
    tableData: {
      CLCARD: {
        ...cariData,
        operation
      }
    }
  })
}

/**
 * Satış faturası için Logo SQL log data oluşturur
 */
export async function createSatisFaturaSqlLogData({
  veritabaniId,
  invoiceData,
  stficheData,
  stlineData,
  operation = 'INSERT'
}: {
  veritabaniId: string
  invoiceData: any
  stficheData?: any
  stlineData?: any[]
  operation?: 'INSERT' | 'UPDATE' | 'DELETE'
}): Promise<string> {
  const tableData: { [key: string]: any } = {
    INVOICE: {
      ...invoiceData,
      operation
    }
  }

  if (stficheData) {
    tableData.STFICHE = {
      ...stficheData,
      operation
    }
  }

  if (stlineData && stlineData.length > 0) {
    tableData.STLINE = stlineData.map(line => ({
      ...line,
      operation
    }))
  }

  return createLogoSqlLogData({
    veritabaniId,
    tableData
  })
}

/**
 * Satın alma faturası için Logo SQL log data oluşturur
 */
export async function createSatinalmaFaturaSqlLogData({
  veritabaniId,
  invoiceData,
  lineData,
  operation = 'INSERT'
}: {
  veritabaniId: string
  invoiceData: any
  lineData?: any[]
  operation?: 'INSERT' | 'UPDATE' | 'DELETE'
}): Promise<string> {
  const tableData: { [key: string]: any } = {
    PURCHINV: {
      ...invoiceData,
      operation
    }
  }

  if (lineData && lineData.length > 0) {
    tableData.PURCHLINE = lineData.map(line => ({
      ...line,
      operation
    }))
  }

  return createLogoSqlLogData({
    veritabaniId,
    tableData
  })
}

/**
 * JSON string'in geçerli olup olmadığını kontrol eder
 */
export function isValidJson(jsonString: string): boolean {
  try {
    JSON.parse(jsonString)
    return true
  } catch {
    return false
  }
}

/**
 * Logo log data'yı güvenli şekilde parse eder
 */
export function parseLogoLogData(jsonString: string | null): any {
  if (!jsonString) return null
  
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    console.error('Logo log data parse edilemedi:', error)
    return null
  }
}
