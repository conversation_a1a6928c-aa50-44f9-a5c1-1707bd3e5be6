import type { ClCardParams, InsertCariHesaplarParams, LogoCariHesap, UpdateCariHesaplarErrorParams, UpdateCariHesaplarLogoRefParams } from '../models/types.ts'
import { randomUUID } from 'node:crypto'
import { consola } from 'consola'
import sql from 'mssql'
import DbService from '../../shared/services/db-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

/**
 * Service for handling Logo SQL operations for Cari hesaplar
 */

const LogoSqlService = {
  checkcariHesapExists: async ({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const result = await logoConnection
        .request()
        .input('code', sql.VarChar, code)
        .query(`
          SELECT CODE
          FROM LG_${logoConfig.erp.firma_numarasi}_CLCARD
          WHERE CODE = @code
          `)
      return result.recordset?.length > 0
    }
    catch (error) {
      consola.error('Cari kart kontrolü sırasında hata oluştu:', error)
      return false
    }
  },
  saveLogoCariHesap: async ({ logoCariHesap, veritabaniId }: { logoCariHesap: LogoCariHesap, veritabaniId: string }): Promise<number> => {
    const dbConnection = await DbService.getConnection('db')
    try {
      const request = dbConnection.request()
      request.input('cariHesapId', sql.Int, logoCariHesap.cariHesapId)
      request.input('code', sql.VarChar, logoCariHesap.code)
      request.input('title', sql.NVarChar, logoCariHesap.title)
      request.input('name', sql.NVarChar, logoCariHesap.name)
      request.input('surname', sql.NVarChar, logoCariHesap.surname)
      request.input('address1', sql.NVarChar, logoCariHesap.address1)
      request.input('address2', sql.NVarChar, logoCariHesap.address2)
      request.input('town', sql.NVarChar, logoCariHesap.town)
      request.input('city', sql.NVarChar, logoCariHesap.city)
      request.input('country', sql.NVarChar, logoCariHesap.country)
      request.input('countryCode', sql.VarChar, logoCariHesap.countryCode)
      request.input('postalCode', sql.VarChar, logoCariHesap.postalCode)
      request.input('auxilCode', sql.VarChar, logoCariHesap.auxilCode)
      request.input('email', sql.VarChar, logoCariHesap.email)
      request.input('taxId', sql.VarChar, logoCariHesap.taxId)
      request.input('tcId', sql.VarChar, logoCariHesap.tcId)
      request.input('accountType', sql.Int, logoCariHesap.accountType)
      request.input('acceptEInv', sql.Int, logoCariHesap.acceptEInv)
      request.input('postLabel', sql.VarChar, logoCariHesap.postLabel)
      request.input('senderLabel', sql.VarChar, logoCariHesap.senderLabel)
      request.input('profileId', sql.VarChar, logoCariHesap.profileId)
      request.input('insteadOfDispatch', sql.Int, logoCariHesap.insteadOfDispatch)
      request.input('veritabani_id', sql.VarChar, veritabaniId)
      const result = await request.query(`
        INSERT INTO LogoCariHesaplar (
        cariHesapId, code, title, name, surname, 
        address1, address2, town, city, country, 
        countryCode, postalCode, auxilCode, email, taxId, tcId,
        accountType, acceptEInv, postLabel, senderLabel, veritabani_id, 
        profileId, insteadOfDispatch, createdAt
        )
        OUTPUT INSERTED.id
        VALUES (
        @cariHesapId, @code, @title, @name, @surname,
        @address1, @address2, @town, @city, @country,
        @countryCode, @postalCode, @auxilCode, @email, @taxId, @tcId,
        @accountType, @acceptEInv, @postLabel, @senderLabel, @veritabani_id, 
        @profileId, @insteadOfDispatch, GETDATE()
          )
          `)
      return result.recordset[0].id
    }
    catch (error) {
      consola.error('Logo cari kart kaydedilirken hata oluştu:', error)
      throw error
    }
  },
  updateCariHesapWithLogoRef: async ({ cariHesapId, logoRef, veritabaniId }: { cariHesapId: number, logoRef: number, veritabaniId: string }): Promise<void> => {
    const dbConnection = await DbService.getConnection('db')
    try {
      const request = dbConnection.request()
      request.input('id', sql.Int, cariHesapId)
      request.input('logoRef', sql.Int, logoRef)
      request.input('veritabani_id', sql.VarChar, veritabaniId)
      await request.query(`
        UPDATE CariHesaplar 
        SET logoRef = @logoRef, updatedAt = GETDATE()
        WHERE id = @id and veritabani_id = @veritabani_id
      `)
    }
    catch (error) {
      consola.error('Cari kart Logo referansı güncellenirken hata oluştu:', error)
      throw error
    }
  },
  updateCariHesapWithError: async ({ cariHesapId, errorMessage, veritabaniId }: { cariHesapId: number, errorMessage: string, veritabaniId: string }): Promise<void> => {
    const dbConnection = await DbService.getConnection('db')
    try {
      const request = dbConnection.request()
      request.input('id', sql.Int, cariHesapId)
      request.input('error', sql.NVarChar, errorMessage)
      request.input('veritabani_id', sql.VarChar, veritabaniId)
      await request.query(`
        UPDATE CariHesaplar 
        SET error = @error, updatedAt = GETDATE()
        WHERE id = @id and veritabani_id = @veritabani_id
        `)
    }
    catch (error) {
      consola.error('Cari kart hata bilgisi güncellenirken hata oluştu:', error)
      throw error
    }
  },

  getcariHesapDetails: async ({ code, veritabaniId }: { code: string, veritabaniId: string }): Promise<{ code: string, vkVeyaTckNo: string, logicalref: number } | null> => {
    const config = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const result = await logoConnection
        .request()
        .input('code', sql.VarChar, code)
        .query(`
          SELECT 
          CODE,
          LOGICALREF,
          IIF(ISPERSCOMP=1, TCKNO, TAXNR) as vknoveyatckno
          FROM LG_${config.erp.firma_numarasi}_CLCARD
          WHERE CODE = @code
          `)
      if (!result.recordset?.length) {
        return null
      }
      const record = result.recordset[0]
      return {
        code: record.CODE,
        vkVeyaTckNo: record.vknoveyatckno,
        logicalref: record.LOGICALREF,
      }
    }
    catch (error) {
      consola.error('Cari kart detayları alınırken hata oluştu:', error)
      return null
    }
  },

  getEfaturaKullan: async ({ veritabaniId }: { veritabaniId: string }): Promise<boolean> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const result = await logoConnection
        .request()
        .input('firmaNo', logoConfig.erp.firma_numarasi)
        .query(`
          SELECT ACCEPTEINV as efatura_kullan 
          FROM ${logoConfig.erp.logodb_master}..L_CAPIFIRM 
          WHERE NR = @firmaNo
        `)
      return result.recordset[0]?.efatura_kullan === 1
    }
    catch (error) {
      consola.error('E-fatura kullanım bilgisi alınırken hata oluştu:', error)
      return false
    }
  },

  getFaturaSenaryo: async ({ veritabaniId }: { veritabaniId: string }): Promise<string> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    try {
      const result = await logoConnection
        .request()
        .input('firmaNo', logoConfig.erp.firma_numarasi)
        .query(`
          SELECT CASE WHEN VALUE=2 THEN 'TİCARİ' ELSE 'TEMEL' END AS fatura_senaryo 
          FROM ${logoConfig.erp.logodb_master}..L_FIRMPARAMS 
          WHERE FIRMNR = @firmaNo and MODULENR=11 and CODE='FIN_CLEINVPROFILEID'
        `)
      return result.recordset[0]?.fatura_senaryo || 'TEMEL'
    }
    catch (error) {
      consola.error('Fatura senaryo bilgisi alınırken hata oluştu:', error)
      return 'TEMEL'
    }
  },

  // Helper to generate a unique GUID across CLCARD and CariHesaplarSql
  async generateUniqueGuid(
    logoConnection: sql.ConnectionPool,
    dbConnection: sql.ConnectionPool,
    firmaNo: string,
  ): Promise<string> {
    let guid: string = ''
    let exists = true
    while (exists) {
      guid = randomUUID()
      const clcardRes = await logoConnection.request()
        .input('guid', guid)
        .query(`SELECT 1 FROM LG_${firmaNo}_CLCARD WHERE GUID = @guid`)
      const cariRes = await dbConnection.request()
        .input('guid', guid)
        .query(`SELECT 1 FROM CariHesaplarSql WHERE GUID = @guid`)
      exists = (clcardRes.recordset.length > 0) || (cariRes.recordset.length > 0)
    }
    return guid.toUpperCase()
  },

  /**
   * Insert a new CLCARD record into Logo
   */
  insertClCard: async ({ veritabaniId, params }: { veritabaniId: string, params: ClCardParams }) => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    const request = logoConnection.request()
    // Set all params as input
    Object.entries(params).forEach(([key, value]) => {
      request.input(key, value)
    })
    const insertQuery = `INSERT INTO LG_${logoConfig.erp.firma_numarasi}_CLCARD (
      CARDTYPE, DEFINITION_, SPECODE, ADDR1, ADDR2, CITY, COUNTRY, POSTCODE, EMAILADDR, CAPIBLOCK_CREATEDMIN,
      PURCHBRWS, SALESBRWS, IMPBRWS, EXPBRWS, FINBRWS, ISPERSCOMP, CODE, TAXNR, TCKNO, NAME, SURNAME, INSTEADOFDESP, TOWN, COUNTRYCODE, GUID,
      CYPHCODE, TELNRS1, TELNRS2, FAXNR, TAXOFFICE, INCHARGE, WEBADDR, WARNEMAILADDR, WARNFAXNR, VATNR,
      BANKBRANCHS1, BANKBRANCHS2, BANKBRANCHS3, BANKBRANCHS4, BANKBRANCHS5, BANKBRANCHS6, BANKBRANCHS7,
      BANKACCOUNTS1, BANKACCOUNTS2, BANKACCOUNTS3, BANKACCOUNTS4, BANKACCOUNTS5, BANKACCOUNTS6, BANKACCOUNTS7,
      DELIVERYMETHOD, DELIVERYFIRM, EDINO, TRADINGGRP, PPGROUPCODE, TAXOFFCODE, TOWNCODE, DISTRICTCODE, DISTRICT, CITYCODE,
      ORDSENDEMAILADDR, ORDSENDFAXNR, DSPSENDEMAILADDR, DSPSENDFAXNR, INVSENDEMAILADDR, INVSENDFAXNR, SUBSCRIBEREXT, AUTOPAIDBANK,
      STORECREDITCARDNO, LOGOID, EXPREGNO, EXPDOCNO, LTRSENDEMAILADDR, LTRSENDFAXNR, CELLPHONE, STATECODE, STATENAME,
      TELCODES1, TELCODES2, FAXCODE, ORGLOGOID, SPECODE2, SPECODE3, SPECODE4, SPECODE5, OFFSENDEMAILADDR, OFFSENDFAXNR,
      BANKNAMES1, BANKNAMES2, BANKNAMES3, BANKNAMES4, BANKNAMES5, BANKNAMES6, BANKNAMES7, MAPID, LONGITUDE, LATITUTE, CITYID, TOWNID,
      BANKIBANS1, BANKIBANS2, BANKIBANS3, BANKIBANS4, BANKIBANS5, BANKIBANS6, BANKIBANS7, EXTSENDEMAILADDR, EXTSENDFAXNR,
      BANKBICS1, BANKBICS2, BANKBICS3, BANKBICS4, BANKBICS5, BANKBICS6, BANKBICS7, INCHARGE2, INCHARGE3, EMAILADDR2, EMAILADDR3,
      EINVOICEID, BANKBCURRENCY1, BANKBCURRENCY2, BANKBCURRENCY3, BANKBCURRENCY4, BANKBCURRENCY5, BANKBCURRENCY6, BANKBCURRENCY7,
      BANKCORRPACC1, BANKCORRPACC2, BANKCORRPACC3, BANKCORRPACC4, BANKCORRPACC5, BANKCORRPACC6, BANKCORRPACC7, BANKVOEN1, BANKVOEN2, BANKVOEN3, BANKVOEN4, BANKVOEN5, BANKVOEN6, BANKVOEN7, DEFINITION2, TELEXTNUMS1, TELEXTNUMS2, FAXEXTNUM, FACEBOOKURL, TWITTERURL, APPLEID, SKYPEID, GLOBALID, ADRESSNO, POSTLABELCODE, SENDERLABELCODE, FBSSENDEMAILADDR, FBSSENDFAXNR, FBASENDEMAILADDR, FBASENDFAXNR, EARCEMAILADDR1, EARCEMAILADDR2, EARCEMAILADDR3, POSTLABELCODEDESP, SENDERLABELCODEDESP, EXIMSENDEMAILADDR, EXIMSENDFAXNR, INCHTELCODES1, INCHTELCODES2, INCHTELCODES3, INCHTELNRS1, INCHTELNRS2, INCHTELNRS3, INCHTELEXTNUMS1, INCHTELEXTNUMS2, INCHTELEXTNUMS3, MERSISNO, COMMRECORDNO, WHATSAPPID, LINKEDINURL, INSTAGRAMURL,
      ACTIVE, DISCRATE, EXTENREF, PAYMENTREF, WARNMETHOD, CLANGUAGE, BLOCKED, CCURRENCY, TEXTINC, SITEID, RECSTATUS, ORGLOGICREF,
      CAPIBLOCK_MODIFIEDBY, CAPIBLOCK_MODIFIEDHOUR, CAPIBLOCK_MODIFIEDMIN, CAPIBLOCK_MODIFIEDSEC, PAYMENTPROC, CRATEDIFFPROC, WFSTATUS, PPGROUPREF, ORDSENDMETHOD, DSPSENDMETHOD, INVSENDMETHOD, SUBSCRIBERSTAT, PAYMENTTYPE, LASTSENDREMLEV, EXTACCESSFLAGS, ORDSENDFORMAT, DSPSENDFORMAT, INVSENDFORMAT, REMSENDFORMAT, CLORDFREQ, ORDDAY, LIDCONFIRMED, EXPBUSTYPREF, INVPRINTCNT, PIECEORDINFLICT, COLLECTINVOICING, EBUSDATASENDTYPE, INISTATUSFLAGS, SLSORDERSTATUS, SLSORDERPRICE, LTRSENDMETHOD, LTRSENDFORMAT, IMAGEINC, SAMEITEMCODEUSE, WFLOWCRDREF, PARENTCLREF, LOWLEVELCODES2, LOWLEVELCODES3, LOWLEVELCODES4, LOWLEVELCODES5, LOWLEVELCODES6, LOWLEVELCODES7, LOWLEVELCODES8, LOWLEVELCODES9, LOWLEVELCODES10, ADDTOREFLIST, TEXTREFTR, TEXTREFEN, ARPQUOTEINC, CLCRM, GRPFIRMNR, CONSCODEREF, OFFSENDMETHOD, OFFSENDFORMAT, EBANKNO, LOANGRPCTRL, LDXFIRMNR, EXTSENDMETHOD, EXTSENDFORMAT, CASHREF, USEDINPERIODS, RSKLIMCR, RSKDUEDATECR, RSKAGINGCR, RSKAGINGDAY, ACCEPTEINV, PROFILEID, PURCORDERSTATUS, PURCORDERPRICE, ISFOREIGN, SHIPBEGTIME1, SHIPBEGTIME2, SHIPBEGTIME3, SHIPENDTIME1, SHIPENDTIME2, SHIPENDTIME3, DBSLIMIT1, DBSLIMIT2, DBSLIMIT3, DBSLIMIT4, DBSLIMIT5, DBSLIMIT6, DBSLIMIT7, DBSTOTAL1, DBSTOTAL2, DBSTOTAL3, DBSTOTAL4, DBSTOTAL5, DBSTOTAL6, DBSTOTAL7, DBSBANKNO1, DBSBANKNO2, DBSBANKNO3, DBSBANKNO4, DBSBANKNO5, DBSBANKNO6, DBSBANKNO7, DBSRISKCNTRL1, DBSRISKCNTRL2, DBSRISKCNTRL3, DBSRISKCNTRL4, DBSRISKCNTRL5, DBSRISKCNTRL6, DBSRISKCNTRL7, DBSBANKCURRENCY1, DBSBANKCURRENCY2, DBSBANKCURRENCY3, DBSBANKCURRENCY4, DBSBANKCURRENCY5, DBSBANKCURRENCY6, DBSBANKCURRENCY7, EINVOICETYPE, DUEDATECOUNT, DUEDATELIMIT, DUEDATETRACK, DUEDATECONTROL1, DUEDATECONTROL2, DUEDATECONTROL3, DUEDATECONTROL4, DUEDATECONTROL5, DUEDATECONTROL6, DUEDATECONTROL7, DUEDATECONTROL8, DUEDATECONTROL9, DUEDATECONTROL10, DUEDATECONTROL11, DUEDATECONTROL12, DUEDATECONTROL13, DUEDATECONTROL14, DUEDATECONTROL15, CLOSEDATECOUNT, CLOSEDATETRACK, DEGACTIVE, DEGCURR, LABELINFO, DEFBNACCREF, PROJECTREF, DISCTYPE, SENDMOD, ISPERCURR, CURRATETYPE, EINVOICETYP, FBSSENDMETHOD, FBSSENDFORMAT, FBASENDMETHOD, FBASENDFORMAT, SECTORMAINREF, SECTORSUBREF, PERSONELCOSTS, FACTORYDIVNR, FACTORYNR, ININVENNR, OUTINVENNR, QTYDEPDURATION, QTYINDEPDURATION, OVERLAPTYPE, OVERLAPAMNT, OVERLAPPERC, BROKERCOMP, CREATEWHFICHE, EINVCUSTOM, SUBCONT, ORDPRIORITY, ACCEPTEDESP, PROFILEIDDESP, LABELINFODESP, ACCEPTEINVPUBLIC, PUBLICBNACCREF, PAYMENTPROCBRANCH, KVKKPERMSTATUS, KVKKANONYSTATUS, EXIMSENDMETHOD, EXIMSENDFORMAT, CLCCANDEDUCT, DRIVERREF, NOTIFYCRDREF, EXCNTRYTYP, EXCNTRYREF, IMCNTRYTYP, IMCNTRYREF, EXIMPAYTYPREF, EXIMBRBANKREF, EXIMCUSTOMREF, EXIMREGTYPREF, EXIMNTFYCLREF, EXIMCNSLTCLREF, EXIMFRGHTCLREF, DISPPRINTCNT, ORDPRINTCNT, CLPTYPEFORPPAYDT, CLSTYPEFORPPAYDT
    ) OUTPUT INSERTED.LOGICALREF VALUES (
      @CARDTYPE, @DEFINITION_, @SPECODE, @ADDR1, @ADDR2, @CITY, @COUNTRY, @POSTCODE, @EMAILADDR, @CAPIBLOCK_CREATEDMIN,
      @PURCHBRWS, @SALESBRWS, @IMPBRWS, @EXPBRWS, @FINBRWS, @ISPERSCOMP, @CODE, @TAXNR, @TCKNO, @NAME, @SURNAME, @INSTEADOFDESP, @TOWN, @COUNTRYCODE, @GUID,
      @CYPHCODE, @TELNRS1, @TELNRS2, @FAXNR, @TAXOFFICE, @INCHARGE, @WEBADDR, @WARNEMAILADDR, @WARNFAXNR, @VATNR,
      @BANKBRANCHS1, @BANKBRANCHS2, @BANKBRANCHS3, @BANKBRANCHS4, @BANKBRANCHS5, @BANKBRANCHS6, @BANKBRANCHS7,
      @BANKACCOUNTS1, @BANKACCOUNTS2, @BANKACCOUNTS3, @BANKACCOUNTS4, @BANKACCOUNTS5, @BANKACCOUNTS6, @BANKACCOUNTS7,
      @DELIVERYMETHOD, @DELIVERYFIRM, @EDINO, @TRADINGGRP, @PPGROUPCODE, @TAXOFFCODE, @TOWNCODE, @DISTRICTCODE, @DISTRICT, @CITYCODE,
      @ORDSENDEMAILADDR, @ORDSENDFAXNR, @DSPSENDEMAILADDR, @DSPSENDFAXNR, @INVSENDEMAILADDR, @INVSENDFAXNR, @SUBSCRIBEREXT, @AUTOPAIDBANK,
      @STORECREDITCARDNO, @LOGOID, @EXPREGNO, @EXPDOCNO, @LTRSENDEMAILADDR, @LTRSENDFAXNR, @CELLPHONE, @STATECODE, @STATENAME,
      @TELCODES1, @TELCODES2, @FAXCODE, @ORGLOGOID, @SPECODE2, @SPECODE3, @SPECODE4, @SPECODE5, @OFFSENDEMAILADDR, @OFFSENDFAXNR,
      @BANKNAMES1, @BANKNAMES2, @BANKNAMES3, @BANKNAMES4, @BANKNAMES5, @BANKNAMES6, @BANKNAMES7, @MAPID, @LONGITUDE, @LATITUTE, @CITYID, @TOWNID,
      @BANKIBANS1, @BANKIBANS2, @BANKIBANS3, @BANKIBANS4, @BANKIBANS5, @BANKIBANS6, @BANKIBANS7, @EXTSENDEMAILADDR, @EXTSENDFAXNR,
      @BANKBICS1, @BANKBICS2, @BANKBICS3, @BANKBICS4, @BANKBICS5, @BANKBICS6, @BANKBICS7, @INCHARGE2, @INCHARGE3, @EMAILADDR2, @EMAILADDR3,
      @EINVOICEID, @BANKBCURRENCY1, @BANKBCURRENCY2, @BANKBCURRENCY3, @BANKBCURRENCY4, @BANKBCURRENCY5, @BANKBCURRENCY6, @BANKBCURRENCY7,
      @BANKCORRPACC1, @BANKCORRPACC2, @BANKCORRPACC3, @BANKCORRPACC4, @BANKCORRPACC5, @BANKCORRPACC6, @BANKCORRPACC7, @BANKVOEN1, @BANKVOEN2, @BANKVOEN3, @BANKVOEN4, @BANKVOEN5, @BANKVOEN6, @BANKVOEN7, @DEFINITION2, @TELEXTNUMS1, @TELEXTNUMS2, @FAXEXTNUM, @FACEBOOKURL, @TWITTERURL, @APPLEID, @SKYPEID, @GLOBALID, @ADRESSNO, @POSTLABELCODE, @SENDERLABELCODE, @FBSSENDEMAILADDR, @FBSSENDFAXNR, @FBASENDEMAILADDR, @FBASENDFAXNR, @EARCEMAILADDR1, @EARCEMAILADDR2, @EARCEMAILADDR3, @POSTLABELCODEDESP, @SENDERLABELCODEDESP, @EXIMSENDEMAILADDR, @EXIMSENDFAXNR, @INCHTELCODES1, @INCHTELCODES2, @INCHTELCODES3, @INCHTELNRS1, @INCHTELNRS2, @INCHTELNRS3, @INCHTELEXTNUMS1, @INCHTELEXTNUMS2, @INCHTELEXTNUMS3, @MERSISNO, @COMMRECORDNO, @WHATSAPPID, @LINKEDINURL, @INSTAGRAMURL,
      @ACTIVE, @DISCRATE, @EXTENREF, @PAYMENTREF, @WARNMETHOD, @CLANGUAGE, @BLOCKED, @CCURRENCY, @TEXTINC, @SITEID, @RECSTATUS, @ORGLOGICREF,
      @CAPIBLOCK_MODIFIEDBY, @CAPIBLOCK_MODIFIEDHOUR, @CAPIBLOCK_MODIFIEDMIN, @CAPIBLOCK_MODIFIEDSEC, @PAYMENTPROC, @CRATEDIFFPROC, @WFSTATUS, @PPGROUPREF, @ORDSENDMETHOD, @DSPSENDMETHOD, @INVSENDMETHOD, @SUBSCRIBERSTAT, @PAYMENTTYPE, @LASTSENDREMLEV, @EXTACCESSFLAGS, @ORDSENDFORMAT, @DSPSENDFORMAT, @INVSENDFORMAT, @REMSENDFORMAT, @CLORDFREQ, @ORDDAY, @LIDCONFIRMED, @EXPBUSTYPREF, @INVPRINTCNT, @PIECEORDINFLICT, @COLLECTINVOICING, @EBUSDATASENDTYPE, @INISTATUSFLAGS, @SLSORDERSTATUS, @SLSORDERPRICE, @LTRSENDMETHOD, @LTRSENDFORMAT, @IMAGEINC, @SAMEITEMCODEUSE, @WFLOWCRDREF, @PARENTCLREF, @LOWLEVELCODES2, @LOWLEVELCODES3, @LOWLEVELCODES4, @LOWLEVELCODES5, @LOWLEVELCODES6, @LOWLEVELCODES7, @LOWLEVELCODES8, @LOWLEVELCODES9, @LOWLEVELCODES10, @ADDTOREFLIST, @TEXTREFTR, @TEXTREFEN, @ARPQUOTEINC, @CLCRM, @GRPFIRMNR, @CONSCODEREF, @OFFSENDMETHOD, @OFFSENDFORMAT, @EBANKNO, @LOANGRPCTRL, @LDXFIRMNR, @EXTSENDMETHOD, @EXTSENDFORMAT, @CASHREF, @USEDINPERIODS, @RSKLIMCR, @RSKDUEDATECR, @RSKAGINGCR, @RSKAGINGDAY, @ACCEPTEINV, @PROFILEID, @PURCORDERSTATUS, @PURCORDERPRICE, @ISFOREIGN, @SHIPBEGTIME1, @SHIPBEGTIME2, @SHIPBEGTIME3, @SHIPENDTIME1, @SHIPENDTIME2, @SHIPENDTIME3, @DBSLIMIT1, @DBSLIMIT2, @DBSLIMIT3, @DBSLIMIT4, @DBSLIMIT5, @DBSLIMIT6, @DBSLIMIT7, @DBSTOTAL1, @DBSTOTAL2, @DBSTOTAL3, @DBSTOTAL4, @DBSTOTAL5, @DBSTOTAL6, @DBSTOTAL7, @DBSBANKNO1, @DBSBANKNO2, @DBSBANKNO3, @DBSBANKNO4, @DBSBANKNO5, @DBSBANKNO6, @DBSBANKNO7, @DBSRISKCNTRL1, @DBSRISKCNTRL2, @DBSRISKCNTRL3, @DBSRISKCNTRL4, @DBSRISKCNTRL5, @DBSRISKCNTRL6, @DBSRISKCNTRL7, @DBSBANKCURRENCY1, @DBSBANKCURRENCY2, @DBSBANKCURRENCY3, @DBSBANKCURRENCY4, @DBSBANKCURRENCY5, @DBSBANKCURRENCY6, @DBSBANKCURRENCY7, @EINVOICETYPE, @DUEDATECOUNT, @DUEDATELIMIT, @DUEDATETRACK, @DUEDATECONTROL1, @DUEDATECONTROL2, @DUEDATECONTROL3, @DUEDATECONTROL4, @DUEDATECONTROL5, @DUEDATECONTROL6, @DUEDATECONTROL7, @DUEDATECONTROL8, @DUEDATECONTROL9, @DUEDATECONTROL10, @DUEDATECONTROL11, @DUEDATECONTROL12, @DUEDATECONTROL13, @DUEDATECONTROL14, @DUEDATECONTROL15, @CLOSEDATECOUNT, @CLOSEDATETRACK, @DEGACTIVE, @DEGCURR, @LABELINFO, @DEFBNACCREF, @PROJECTREF, @DISCTYPE, @SENDMOD, @ISPERCURR, @CURRATETYPE, @EINVOICETYP, @FBSSENDMETHOD, @FBSSENDFORMAT, @FBASENDMETHOD, @FBASENDFORMAT, @SECTORMAINREF, @SECTORSUBREF, @PERSONELCOSTS, @FACTORYDIVNR, @FACTORYNR, @ININVENNR, @OUTINVENNR, @QTYDEPDURATION, @QTYINDEPDURATION, @OVERLAPTYPE, @OVERLAPAMNT, @OVERLAPPERC, @BROKERCOMP, @CREATEWHFICHE, @EINVCUSTOM, @SUBCONT, @ORDPRIORITY, @ACCEPTEDESP, @PROFILEIDDESP, @LABELINFODESP, @ACCEPTEINVPUBLIC, @PUBLICBNACCREF, @PAYMENTPROCBRANCH, @KVKKPERMSTATUS, @KVKKANONYSTATUS, @EXIMSENDMETHOD, @EXIMSENDFORMAT, @CLCCANDEDUCT, @DRIVERREF, @NOTIFYCRDREF, @EXCNTRYTYP, @EXCNTRYREF, @IMCNTRYTYP, @IMCNTRYREF, @EXIMPAYTYPREF, @EXIMBRBANKREF, @EXIMCUSTOMREF, @EXIMREGTYPREF, @EXIMNTFYCLREF, @EXIMCNSLTCLREF, @EXIMFRGHTCLREF, @DISPPRINTCNT, @ORDPRINTCNT, @CLPTYPEFORPPAYDT, @CLSTYPEFORPPAYDT
    )`
    const result = await request.query(insertQuery)
    return result.recordset[0].LOGICALREF
  },

  /**
   * Insert a new record into CariHesaplarSql for tracking
   */
  insertCariHesaplarSql: async ({ veritabaniId: _veritabaniId, params }: { veritabaniId: string, params: Record<string, unknown> }) => {
    const dbConnection = await DbService.getConnection('db')
    const request = dbConnection.request()

    // Add all params as SQL input parameters
    Object.entries(params).forEach(([key, value]) => {
      request.input(key, value)
    })

    // Using a dynamic SQL approach to handle the large number of fields
    // Get all object properties for the column list
    const columns = `${Object.keys(params).join(', ')}, createdAt, updatedAt`

    // Create parameter placeholders for values (@COLUMN_NAME)
    const parameterPlaceholders = `${Object.keys(params).map(key => `@${key}`).join(', ')}, GETDATE(), GETDATE()`

    // Build and execute the query
    try {
      await request.query(`
        INSERT INTO CariHesaplarSql (
          ${columns}
        ) VALUES (
          ${parameterPlaceholders}
        )
      `)
    }
    catch (error) {
      consola.error('CariHesaplarSql kaydedilirken hata oluştu:', error)
      throw error
    }
  },

  /**
   * Insert a new record into CariHesaplar and return inserted id
   */
  insertCariHesaplar: async ({ veritabaniId, params }: { veritabaniId: string, params: InsertCariHesaplarParams }) => {
    const dbConnection = await DbService.getConnection('db')
    const request = dbConnection.request()
    request.input('kodu', params.kodu)
    request.input('vkVeyaTckNo', params.vkVeyaTckNo)
    request.input('unvan', params.unvan)
    request.input('ad', params.ad)
    request.input('soyad', params.soyad)
    request.input('adres', params.adres)
    request.input('il', params.il)
    request.input('ilce', params.ilce)
    request.input('ulke', params.ulke || 'TÜRKİYE')
    request.input('ulkeKodu', params.ulkeKodu || 'TR')
    request.input('email', params.email)
    request.input('postaKodu', params.postaKodu)
    request.input('ozelKod', params.ozelKod)
    request.input('logoRef', params.logoRef ?? 0)
    request.input('veritabani_id', veritabaniId)
    const result = await request.query(`
      INSERT INTO CariHesaplar (
        kodu, vkVeyaTckNo, unvan, ad, soyad, adres, 
        il, ilce, ulke, ulkeKodu, email, 
        postaKodu, ozelKod, logoRef, veritabani_id, createdAt, updatedAt
      )
      OUTPUT INSERTED.id
      VALUES (
        @kodu, @vkVeyaTckNo, @unvan, @ad, @soyad, @adres,
        @il, @ilce, @ulke, @ulkeKodu, @email,
        @postaKodu, @ozelKod, @logoRef, @veritabani_id, GETDATE(), GETDATE()
      )
    `)
    return result.recordset[0].id
  },

  /**
   * Update logoRef in CariHesaplar by id
   */
  updateCariHesaplarLogoRef: async ({ veritabaniId, params }: { veritabaniId: string, params: UpdateCariHesaplarLogoRefParams }) => {
    const dbConnection = await DbService.getConnection('db')
    const request = dbConnection.request()
    request.input('id', params.id)
    request.input('logoRef', params.logoRef)
    request.input('veritabani_id', veritabaniId)
    await request.query(`
      UPDATE CariHesaplar
      SET logoRef = @logoRef, updatedAt = GETDATE()
      WHERE id = @id AND veritabani_id = @veritabani_id
    `)
  },

  /**
   * Update error in CariHesaplar by id
   */
  updateCariHesaplarError: async ({ veritabaniId, params }: { veritabaniId: string, params: UpdateCariHesaplarErrorParams }) => {
    const dbConnection = await DbService.getConnection('db')
    const request = dbConnection.request()
    request.input('id', params.id)
    request.input('error', params.error)
    request.input('veritabani_id', veritabaniId)
    await request.query(`
      UPDATE CariHesaplar
      SET error = @error, updatedAt = GETDATE()
      WHERE id = @id AND veritabani_id = @veritabani_id
    `)
  },
}

export default LogoSqlService
