/**
 * SQL queries for 'Markalar' module initialization and operations
 */
export const query = `
-- Table: Markalar
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Mark<PERSON>r' and xtype='U')
BEGIN
    CREATE TABLE Markalar (
        id INT PRIMARY KEY IDENTITY(1,1),
        kodu VARCHAR(25) NOT NULL,
        adi VARCHAR(100) NOT NULL,
        aciklama VARCHAR(250),
        veritabani_id VARCHAR(37) NOT NULL,
        logoRef INT,
        error NVARCHAR(MAX),
        logo_rest_data VARCHAR(MAX) NULL,
        logo_sql_data VARCHAR(MAX) NULL,
        createdAt DATETIME2 NOT NULL DEFAULT GETDATE(),
        updatedAt DATETIME2 NOT NULL DEFAULT GETDATE()
    );
    
    CREATE UNIQUE INDEX IX_Markalar_kodu_veritabani ON Markalar(kodu, veritabani_id);
    PRINT '<PERSON><PERSON><PERSON> tablosu ba<PERSON>ar<PERSON>yla oluşturuldu';
END
ELSE
BEGIN
    PRINT '<PERSON><PERSON>r tablosu zaten mevcut';
END;
`
