# VNS Mizan REST Service - Proje <PERSON>mantasyonu

## Proje <PERSON>l <PERSON>

**Proje Adı:** VNS Mizan REST Service  
**Geliştirici:** Venüs B<PERSON>şim  
**Amaç:** Mizan Mağaza ve Market Satış Yönetimi Programı ile Logo ERP Programları arasında veri entegrasyonu sağlayan web servisi  
**Mevcut Odak:** Backend geliştirme (@vns-mizan-rest-service/) - öncelik  
**Gelecek Faz:** Frontend geliştirme (@vns-mizan-frontend/)  

## Proje Mimarisi

### Monorepo Yapısı
```
vns-mizan-entegrasyon/
├── vns-mizan-rest-service/     # Backend API servisi (Ana odak)
├── vns-mizan-frontend/         # React frontend (Gelecek faz)
├── package.json                # Root workspace konfigürasyonu
├── pnpm-workspace.yaml         # PNPM workspace tanımları
└── tsconfig.json              # TypeScript konfigürasyonu
```

### Backend Teknoloji Stack
- **Runtime:** Node.js (Latest)
- **Language:** TypeScript
- **Framework:** Express.js
- **Database:** Microsoft SQL Server (MSSQL)
- **Package Manager:** PNPM
- **Process Management:** Node.js Cluster API
- **Documentation:** Swagger/OpenAPI
- **Security:** Helmet, CORS, Session Management

## Veri Entegrasyon Mimarisi

### Giden Entegrasyon (Mizan → Logo ERP) - İki Yöntem

#### Yöntem 1: Logo REST Service Entegrasyonu (Tercih Edilen)
- **Uygulama:** Resmi Logo ERP REST API servisi kullanımı
- **Avantajlar:** Temiz entegrasyon, iş mantığı ERP sistemi tarafından yönetilir
- **Sınırlamalar:** Logo ERP lisans anlaşmasına bağlı kullanılabilirlik
- **Durum:** Mevcut olduğunda tercih edilen yöntem
- **Konfigürasyon:** `config.json` içinde `rest_settings.use_rest: true`

#### Yöntem 2: Doğrudan Veritabanı Entegrasyonu (Yedek)
- **Uygulama:** SQL ile doğrudan Logo ERP veritabanına kayıt yazma
- **Avantajlar:** Lisans kısıtlamalarından bağımsız her zaman kullanılabilir
- **Dezavantajlar:** İş mantığı, doğrulama kuralları ve veri bütünlüğü kontrollerinin manuel uygulanması gerekir
- **Riskler:** ERP'nin yerleşik güvenlik önlemlerini atlar - "hacky" yaklaşım olarak kabul edilir
- **Durum:** Yalnızca Yöntem 1 kullanılamadığında kullanılır
- **Konfigürasyon:** `config.json` içinde `rest_settings.use_rest: false`

### Gelen Entegrasyon (Logo ERP → Mizan)
- **Tek Yöntem:** SQL ile Logo ERP veritabanından doğrudan sorgu

## İş Modülleri

### Ana Modüller
1. **cariler** - Cari hesap yönetimi
2. **stoklar** - Stok bilgileri yönetimi
3. **satis-faturalari** - Satış fatura işlemleri
4. **satinalma-faturalari** - Satın alma fatura işlemleri
5. **reyonlar** - Reyon tanımları
6. **alt-gruplar** - Alt grup kategorileri
7. **ana-gruplar** - Ana grup kategorileri
8. **kategoriler** - Ürün kategorileri
9. **markalar** - Marka bilgileri
10. **cari-personel** - Cari personel yönetimi
11. **logo-kullanicilari** - Logo kullanıcı bilgileri
12. **logo-veritabani-baglantilari** - Logo veritabanı bağlantı yönetimi
13. **isyeri-fabrika-ambar-bilgileri** - İşyeri/fabrika/ambar bilgileri
14. **bolum-bilgileri** - Bölüm tanımları

### Paylaşılan Servisler
- **DbService** - Veritabanı bağlantı havuzu yönetimi
- **DbInitService** - Veritabanı başlatma ve migrasyon
- **LogoLookupService** - Logo ERP referans değer arama
- **LogoTrackingService** - Logo işlem takip tabloları
- **AuthService** - Kimlik doğrulama ve oturum yönetimi

## Veritabanı Şeması

### Ana Uygulama Veritabanı (VnsMizanEntegrasyonDb)
- **users** - Sistem kullanıcıları
- **sessions** - Oturum bilgileri
- **CariHesaplar** - Cari hesap kayıtları
- **CariHesaplarSql** - Logo cari hesap SQL yapısı
- **SatisFaturalari** - Satış fatura kayıtları
- **SatisFaturalariSatirlari** - Satış fatura satır detayları
- **SatinalmaFaturalari** - Satın alma fatura kayıtları
- **SatinalmaFaturalariSatirlari** - Satın alma fatura satır detayları
- **LogoInvoice** - Logo fatura takip tablosu
- **LogoStfiche** - Logo irsaliye takip tablosu
- **LogoStline** - Logo fatura satır takip tablosu

### Logo ERP Veritabanı Entegrasyonu
- **LG_XXX_CLCARD** - Cari hesap kartları
- **LG_XXX_INVOICE** - Fatura başlık bilgileri
- **LG_XXX_STFICHE** - İrsaliye bilgileri
- **LG_XXX_STLINE** - Fatura/irsaliye satır detayları
- **LG_XXX_ITEMS** - Stok kartları
- **LG_XXX_PRCLIST** - Fiyat listeleri

## Konfigürasyon Yönetimi

### config.json Yapısı
```json
{
  "node_env": "development|production",
  "database_connections": {
    "db": { /* Ana uygulama veritabanı */ }
  },
  "project_settings": {
    "port": 3000,
    "instance_count": 5,
    "https": { /* HTTPS ayarları */ },
    "session": { /* Oturum ayarları */ },
    "admin": { /* Yönetici hesap bilgileri */ },
    "mail": { /* E-posta ayarları */ },
    "logo": {
      "db_connections": [
        {
          "id": "uuid",
          "active": boolean,
          "name": "string",
          "sql": { /* SQL bağlantı bilgileri */ },
          "erp": {
            "rest_settings": {
              "use_rest": boolean, /* Entegrasyon yöntemi seçimi */
              "rest_api_url": "string",
              "client_key": "string"
            }
          }
        }
      ]
    }
  }
}
```

## Hata Yönetimi ve Loglama

### Türkçe Hata Mesajları
- Tüm hata, yanıt ve log mesajları Türkçe dilinde
- Kullanıcı dostu hata mesajları
- Detaylı geliştirici logları

### Hata Yönetim Stratejisi
- **Graceful Error Handling** - Sistem çökmeden hata yönetimi
- **Error Tracking** - Hata kayıtlarının veritabanında saklanması
- **Fallback Mechanisms** - REST API başarısız olduğunda SQL yöntemine geçiş
- **Transaction Management** - Veritabanı işlemlerinde atomik operasyonlar

## API Dokümantasyonu

### Swagger/OpenAPI Entegrasyonu
- **Endpoint:** `/v1/api-docs`
- **Interactive UI:** Swagger UI Express
- **Auto-generation:** Swagger JSDoc ile otomatik dokümantasyon

### Ana API Endpoints
```
GET  /v1/                           # API bilgileri
POST /v1/auth/login                 # Kullanıcı girişi
POST /v1/auth/logout                # Kullanıcı çıkışı
GET  /v1/cariler                    # Cari hesap listesi
POST /v1/cariler                    # Yeni cari hesap
GET  /v1/stoklar                    # Stok listesi
POST /v1/satis-faturalari          # Satış faturası oluştur
POST /v1/satinalma-faturalari      # Satın alma faturası oluştur
```

## Güvenlik

### Kimlik Doğrulama
- **Session-based Authentication** - Express Session
- **Password Hashing** - Güvenli şifre saklama
- **Session Timeout** - Otomatik oturum sonlandırma

### Güvenlik Middleware
- **Helmet** - HTTP güvenlik başlıkları
- **CORS** - Cross-Origin Resource Sharing
- **Rate Limiting** - İstek sınırlama
- **Input Validation** - Zod ile veri doğrulama

## Performans ve Ölçeklenebilirlik

### Cluster Yönetimi
- **Multi-process Architecture** - Node.js Cluster API
- **Worker Process Management** - CPU çekirdek sayısına göre otomatik ölçekleme
- **Graceful Shutdown** - Güvenli sistem kapatma

### Veritabanı Optimizasyonu
- **Connection Pooling** - MSSQL bağlantı havuzu
- **Query Optimization** - Optimize edilmiş SQL sorguları
- **Index Management** - Performans için indeks kullanımı

## Geliştirme Rehberi

### Kod Standartları
- **TypeScript** - Tip güvenliği
- **ESLint** - Kod kalitesi kontrolü
- **Functional Programming** - Fonksiyonel programlama tercihi
- **RORO Pattern** - Receive Object, Return Object
- **Early Return** - Erken dönüş ile basitleştirilmiş mantık

### Proje Yapısı
```
src/
├── shared/                 # Paylaşılan servisler ve yardımcılar
├── rest-api/              # API routes ve middleware
├── [module-name]/         # İş modülleri
│   ├── models/           # Veri modelleri
│   ├── routes/           # API rotaları
│   └── services/         # İş mantığı servisleri
└── scripts/              # Yardımcı scriptler
```

## Gelecek Geliştirme Yol Haritası

### Kısa Vadeli Hedefler
1. **Logo REST API Entegrasyonu** - Tüm modüller için REST API desteği
2. **Error Recovery** - Gelişmiş hata kurtarma mekanizmaları
3. **Performance Monitoring** - Performans izleme ve raporlama
4. **Unit Testing** - Kapsamlı test coverage

### Uzun Vadeli Hedefler
1. **Frontend Development** - React tabanlı yönetim paneli
2. **Real-time Sync** - Gerçek zamanlı veri senkronizasyonu
3. **Multi-tenant Support** - Çoklu müşteri desteği
4. **Cloud Deployment** - Bulut ortamı dağıtımı

## Kurulum ve Çalıştırma

### Gereksinimler
- Node.js (Latest LTS)
- PNPM
- Microsoft SQL Server
- TypeScript

### Kurulum Adımları
```bash
# Bağımlılıkları yükle
pnpm install

# Geliştirme ortamında çalıştır
pnpm dev

# Production build
pnpm build

# Linting
pnpm lint:fix
```

### Konfigürasyon
1. `config.json` dosyasını düzenle
2. Veritabanı bağlantı bilgilerini güncelle
3. Logo ERP bağlantı ayarlarını yapılandır
4. HTTPS sertifikalarını ayarla (opsiyonel)

---

**Not:** Bu dokümantasyon, VNS Mizan REST Service projesinin mevcut durumunu yansıtmaktadır. Proje aktif geliştirme aşamasında olduğu için düzenli güncellemeler yapılacaktır.
