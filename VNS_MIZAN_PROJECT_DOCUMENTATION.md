# VNS Mizan REST Service - Proje Rehberi

## Projeye Genel Bakış

**Proje:** VNS Mizan REST Service
**Geliştiren:** Venüs <PERSON>
**Ne İşe Yarar:** Mizan Mağaza ve Market Satış Yönetimi Programı ile Logo ERP sistemleri arasında köprü görevi gören web servisi
**Şu An Odaklandığımız Alan:** Backend geliştirme (@vns-mizan-rest-service/) - bu bizim önceliğimiz
**Sonraki Adım:** Frontend geliştirme (@vns-mizan-frontend/)

## Proje Nasıl Yapılandırılmış

### Monorepo Yapımız
```
vns-mizan-entegrasyon/
├── vns-mizan-rest-service/     # Backend API servisi (şu an bunun üzerinde çalışıyoruz)
├── vns-mizan-frontend/         # React frontend (ileride geliştirilecek)
├── package.json                # Ana workspace ayarları
├── pnpm-workspace.yaml         # PNPM workspace tanımları
└── tsconfig.json              # TypeScript ayarları
```

### Backend'de Kullandığımız Teknolojiler
- **Çalışma Ortamı:** Node.js (en güncel sürüm)
- **Programlama Dili:** TypeScript
- **Web Framework:** Express.js
- **Veritabanı:** Microsoft SQL Server (MSSQL)
- **Paket Yöneticisi:** PNPM
- **Süreç Yönetimi:** Node.js Cluster API
- **API Dokümantasyonu:** Swagger/OpenAPI
- **Güvenlik:** Helmet, CORS, Session Management

## Çoklu Müşteri Destekli Veri Entegrasyon Sistemi

VNS Mizan REST Service, aynı anda birden fazla Logo ERP kurulumu ile çalışabilen gelişmiş bir yapıya sahip. Bu sayede farklı firmaların farklı dönemlerindeki verilerini aynı sistem üzerinden yönetebiliyoruz. Sistem, SaaS mantığıyla çalışarak ölçeklenebilir bir çözüm sunuyor.

### Çoklu Müşteri Yapısının Özellikleri

#### Nasıl Yapılandırılıyor
- **Birden Fazla Bağlantı:** `config.json` dosyasındaki `logo.db_connections` listesi ile birden çok Logo ERP'ye bağlanabiliyoruz
- **Her Bağlantı Benzersiz:** Her bağlantının kendine özel UUID'si var
- **Firma ve Dönem Esnekliği:** Her bağlantı farklı firma numarası ve dönem kombinasyonu kullanabiliyor
- **Bağımsız Ayarlar:** Her bağlantı kendi entegrasyon yöntemini seçebiliyor
- **Açıp Kapatabilme:** `active` ayarı ile bağlantıları istediğimiz zaman aktif/pasif yapabiliyoruz

#### Veri Nasıl Yönetiliyor
- **Hedef Belirleme:** API istekleri `veritabani_id` parametresi ile hangi firma/döneme gideceğini belirtiyor
- **Otomatik Yönlendirme:** Sistem kendiliğinden doğru Logo ERP bağlantısına yönlendiriyor
- **Veri Ayrımı:** Farklı firmaların ve dönemlerin verileri birbirine karışmıyor
- **Dinamik Tablo İsimleri:** Firma numarasına göre tablo isimleri otomatik oluşturuluyor (mesela: `LG_004_CLCARD`, `LG_005_ITEMS`)

### Mizan'dan Logo'ya Veri Gönderme - İki Farklı Yol

#### 1. Yol: Logo REST API Kullanımı (Öncelikli Tercihimiz)
- **Nasıl Çalışıyor:** Logo'nun resmi REST API servisini kullanıyoruz
- **Avantajları:** Temiz bir entegrasyon, iş kuralları Logo tarafından kontrol ediliyor
- **Sınırlaması:** Logo ERP lisansına bağlı, her zaman kullanılamayabiliyor
- **Ne Zaman Kullanıyoruz:** Mümkün olduğunda hep bunu tercih ediyoruz
- **Nasıl Ayarlıyoruz:** Her bağlantı için `rest_settings.use_rest: true` yapıyoruz
- **Çoklu Müşteri:** Her firma/dönem kendi başına REST API kullanabiliyor

#### 2. Yol: Doğrudan Veritabanına Yazma (Yedek Plan)
- **Nasıl Çalışıyor:** SQL ile doğrudan Logo ERP veritabanına veri yazıyoruz
- **Avantajları:** Lisans durumuna bakmadan her zaman kullanılabiliyor
- **Dezavantajları:** İş kurallarını, kontrolleri kendimiz yapmak zorundayız
- **Riski:** Logo'nun kendi güvenlik kontrollerini atlıyoruz - biraz "hacky" bir yaklaşım
- **Ne Zaman Kullanıyoruz:** Sadece 1. yol çalışmadığında
- **Nasıl Ayarlıyoruz:** Her bağlantı için `rest_settings.use_rest: false` yapıyoruz
- **Çoklu Müşteri:** Karışık kullanım mümkün (bazı firmalar REST, bazıları SQL)

### Logo'dan Mizan'a Veri Çekme
- **Tek Yöntem:** SQL ile Logo ERP veritabanından doğrudan okuma yapıyoruz
- **Çoklu Müşteri Desteği:** `veritabani_id` ile hangi firma/dönemi hedeflediğimizi belirtiyoruz
- **Dinamik Sorgular:** Firma numarasına göre tablo isimlerini otomatik oluşturuyoruz

## İş Modülleri

### Ana Modüllerimiz
1. **cariler** - Müşteri ve tedarikçi bilgilerini yönetiyoruz
2. **stoklar** - Ürün ve stok bilgilerini takip ediyoruz
3. **satis-faturalari** - Satış faturalarını işliyoruz
4. **satinalma-faturalari** - Satın alma faturalarını yönetiyoruz
5. **reyonlar** - Mağaza reyon tanımlarını yapıyoruz
6. **alt-gruplar** - Ürün alt grup kategorilerini düzenliyoruz
7. **ana-gruplar** - Ürün ana grup kategorilerini yönetiyoruz
8. **kategoriler** - Ürün kategorilerini organize ediyoruz
9. **markalar** - Marka bilgilerini saklıyoruz
10. **cari-personel** - Cari hesaplara bağlı personel bilgilerini tutuyoruz
11. **logo-kullanicilari** - Logo sistemindeki kullanıcı bilgilerini alıyoruz
12. **logo-veritabani-baglantilari** - Logo veritabanı bağlantılarını yönetiyoruz
13. **isyeri-fabrika-ambar-bilgileri** - İşyeri, fabrika ve ambar bilgilerini düzenliyoruz
14. **bolum-bilgileri** - Şirket bölüm tanımlarını yapıyoruz

### Ortak Kullandığımız Servisler
- **DbService** - Veritabanı bağlantılarını havuz halinde yönetiyoruz
- **DbInitService** - Veritabanını başlatıp gerekli tabloları oluşturuyoruz
- **LogoLookupService** - Logo ERP'den referans değerleri arıyoruz
- **LogoTrackingService** - Logo işlemlerini takip ediyoruz
- **AuthService** - Kullanıcı girişi ve oturum yönetimi yapıyoruz

## Veritabanı Yapımız

### Ana Uygulama Veritabanımız (VnsMizanEntegrasyonDb)
- **users** - Sisteme giriş yapan kullanıcıları saklıyoruz
- **sessions** - Kullanıcı oturum bilgilerini tutuyoruz
- **CariHesaplar** - Müşteri ve tedarikçi kayıtlarını burada saklıyoruz
- **CariHesaplarSql** - Logo cari hesap verilerinin SQL yapısını tutuyoruz
- **SatisFaturalari** - Satış fatura kayıtlarımız burada
- **SatisFaturalariSatirlari** - Satış faturalarının satır detayları
- **SatinalmaFaturalari** - Satın alma fatura kayıtlarımız
- **SatinalmaFaturalariSatirlari** - Satın alma faturalarının satır detayları
- **LogoInvoice** - Logo'ya gönderdiğimiz faturaları takip ediyoruz
- **LogoStfiche** - Logo irsaliye işlemlerini takip ediyoruz
- **LogoStline** - Logo fatura satır işlemlerini takip ediyoruz

### Logo ERP Veritabanı ile Entegrasyon
- **LG_XXX_CLCARD** - Logo'daki cari hesap kartları
- **LG_XXX_INVOICE** - Logo'daki fatura başlık bilgileri
- **LG_XXX_STFICHE** - Logo'daki irsaliye bilgileri
- **LG_XXX_STLINE** - Logo'daki fatura/irsaliye satır detayları
- **LG_XXX_ITEMS** - Logo'daki stok kartları
- **LG_XXX_PRCLIST** - Logo'daki fiyat listeleri

## Konfigürasyon Yönetimi

### Multi-Tenant config.json Yapısı
```json
{
  "node_env": "development|production",
  "database_connections": {
    "db": { /* Ana uygulama veritabanı */ }
  },
  "project_settings": {
    "port": 3000,
    "instance_count": 5,
    "https": { /* HTTPS ayarları */ },
    "session": { /* Oturum ayarları */ },
    "admin": { /* Yönetici hesap bilgileri */ },
    "mail": { /* E-posta ayarları */ },
    "logo": {
      "db_connections": [
        {
          "id": "ca108341-f2d1-44a3-b0db-9d7871917758", /* Benzersiz UUID */
          "active": false, /* Bağlantı aktif/pasif durumu */
          "name": "Bayi seti 2024", /* Açıklayıcı isim */
          "sql": {
            "server": "MERT-PC",
            "database": "TigerDb",
            "user": "sa",
            "password": "Logo123",
            "port": 1433,
            "encrypt": false,
            "trust_server_certificate": true
          },
          "erp": {
            "logodb_master": "TigerDb", /* Ana Logo veritabanı */
            "firma_numarasi": "004", /* Logo firma numarası */
            "donem_numarasi": "01", /* Logo dönem numarası */
            "kullanici_adi": "LOGOYENI", /* Logo kullanıcı adı */
            "sifre": "LOGOYENI", /* Logo şifresi */
            "elogo": {
              "web_service_url": "https://pb-g.elogo.com.tr/PostBoxService.svc"
            },
            "rest_settings": {
              "use_rest": false, /* Bu bağlantı için entegrasyon yöntemi */
              "rest_api_url": "http://127.0.0.1:32001/api/v1",
              "client_key": "VkVOVVM6dmI0ank5TlN6ckRQUkxQV3NKcTY3c3RPTVhESGlyUFZOcktEVnRHTmY3TT0="
            }
          }
        },
        {
          "id": "2726b8c5-75ce-461d-a771-08b07bad99b0", /* Farklı firma/dönem */
          "active": false,
          "name": "Bayi seti 2025",
          "sql": { /* Aynı veya farklı SQL server */ },
          "erp": {
            "firma_numarasi": "005", /* Farklı firma numarası */
            "donem_numarasi": "01", /* Aynı veya farklı dönem */
            "rest_settings": {
              "use_rest": true /* Bu bağlantı REST API kullanır */
            }
          }
        }
      ]
    }
  }
}
```

### Multi-Tenant Konfigürasyon Özellikleri
- **Benzersiz Tanımlama:** Her bağlantı UUID ile benzersiz şekilde tanımlanır
- **Esnek Firma/Dönem:** Farklı firma numaraları ve dönem kombinasyonları
- **Bağımsız Entegrasyon:** Her bağlantı kendi entegrasyon yöntemini seçer
- **Dinamik Aktivasyon:** `active` flag ile bağlantıları etkinleştirme/devre dışı bırakma
- **Karma Yaklaşım:** Bazı bağlantılar REST API, bazıları doğrudan SQL kullanabilir

## Kapsamlı Loglama ve Hata Takip Sistemi

VNS Mizan sistemi, veri doğrulama, dönüştürme ve entegrasyon süreçlerinin karmaşıklığı nedeniyle Mizan tarafından gelen tüm POST istekleri için SQL veritabanına detaylı loglama gerektirir.

### Kritik Loglama Senaryoları

#### 1. İstek Doğrulama Hataları
- **Zod Schema Validation:** Gelen Mizan verilerinin şema doğrulaması başarısız olduğunda loglama
- **Veri Tipi Hataları:** Beklenen veri tiplerinin uyuşmaması durumları
- **Zorunlu Alan Eksiklikleri:** Gerekli alanların eksik olması durumları

#### 2. Logo REST API Hataları
- **API Yanıt Hataları:** Logo REST API'den gelen hata yanıtlarının yakalanması ve loglanması
- **Bağlantı Hataları:** REST API bağlantı sorunları
- **Kimlik Doğrulama Hataları:** Token alma ve yenileme hataları
- **Rate Limiting:** API çağrı sınırı aşımları

#### 3. Doğrudan SQL Entegrasyon Hataları
- **SQL Execution Errors:** Doğrudan veritabanı işlemleri sırasında SQL yürütme hataları
- **Constraint Violations:** Veritabanı kısıtlama ihlalleri
- **Transaction Rollbacks:** İşlem geri alma durumları
- **Connection Pool Issues:** Bağlantı havuzu sorunları

#### 4. Veri Dönüştürme Sorunları
- **Format Conversion Errors:** Mizan-Logo veri formatı dönüştürme hataları
- **Data Mapping Issues:** Alan eşleme sorunları
- **Currency/Date Conversion:** Para birimi ve tarih dönüştürme hataları
- **Encoding Problems:** Karakter kodlama sorunları

#### 5. İş Mantığı Doğrulama Hataları
- **Business Rule Violations:** Şema doğrulamasını geçen ancak iş kurallarını ihlal eden veriler
- **Logo Reference Validation:** Logo referans değer doğrulama hataları
- **Cross-Module Dependencies:** Modüller arası bağımlılık hataları
- **Data Consistency Checks:** Veri tutarlılık kontrol hataları

#### 6. Kısmi Başarı Senaryoları
- **Partial Operations:** Çok adımlı süreçlerde bazı işlemlerin başarılı, bazılarının başarısız olması
- **Batch Processing Errors:** Toplu işlem sırasında kısmi hatalar
- **Multi-Line Invoice Issues:** Çok satırlı faturalarda satır bazlı hatalar
- **Rollback Scenarios:** Kısmi başarı sonrası geri alma durumları

#### 7. Logo Referans Çözümleme Hataları
- **Customer Code Lookup Failures:** Müşteri kodu arama işlemlerinin başarısız olması
- **Item Code Resolution Issues:** Ürün kodu çözümleme sorunları
- **Salesman Reference Errors:** Satış temsilcisi referans hataları
- **Project Code Validation:** Proje kodu doğrulama hataları

### Gelişmiş Logo Entegrasyon Loglama Sistemi

#### Yeni Loglama Mimarisi
VNS Mizan artık Logo ERP entegrasyonlarında gönderilen tüm veriyi yapılandırılmış JSON formatında saklıyor. Bu sistem, Logo'nun yüzlerce veritabanı sütununun karmaşıklığını organize edilmiş JSON yapılarında çözerek basitleştiriyor.

#### JSON Tabanlı Loglama Sütunları
Her iş modülü tablosuna iki yeni sütun eklendi:
- **logo_rest_data (VARCHAR(MAX))** - Logo REST API'ye gönderilen tam JSON verisi
- **logo_sql_data (VARCHAR(MAX))** - Logo SQL tablolarına yazılan verinin JSON formatı

#### Logo REST API Loglama Formatı
```json
{
  "endpoint": "/api/v1/Arps",
  "method": "POST",
  "timestamp": "2024-01-15T10:30:00Z",
  "payload": { /* Logo REST API'ye gönderilen tam JSON */ },
  "response_status": 200,
  "response_data": { /* Logo'dan gelen yanıt */ },
  "error": "Hata mesajı (varsa)"
}
```

#### Logo SQL Loglama Formatı
```json
{
  "LG_005_CLCARD": {
    "LOGICALREF": 98765,
    "CODE": "CARI001",
    "DEFINITION_": "Müşteri Adı",
    "operation": "INSERT",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "LG_005_01_INVOICE": {
    "LOGICALREF": 54321,
    "FICHENO": "SF000001",
    "DATE_": "2024-01-15",
    "operation": "INSERT",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

#### Implementasyon Durumu
- **Ana İşlemler:** Faturalar ve müşteri kayıtları için gelişmiş JSON loglama aktif
- **Türkçe Mesajlar:** Tüm hata mesajları Türkçe dilinde
- **Otomatik Loglama:** Logo'ya gönderim öncesi otomatik veri loglama
- **Hata Kurtarma:** Başarısız işlemleri tam veriyle tekrar oynatabilme

#### Loglama Sisteminin Faydaları
- **Tam Görünürlük:** Logo'ya gönderilen veriye tam görünürlük
- **Kolay Debugging:** Başarısız operasyonları tam veriyle debug edebilme
- **Audit Trail:** Tüm Logo entegrasyon denemelerinin tam audit trail'i
- **Basit Şema:** Yüzlerce Logo-spesifik sütun oluşturmaktan kaçınma
- **Esneklik:** Şema değişikliği olmadan yeni Logo tablolarını kolayca ekleme

### Türkçe Hata Mesajları
- Tüm hata, yanıt ve log mesajları Türkçe dilinde
- Kullanıcı dostu hata mesajları
- Detaylı geliştirici logları
- Hata kodları ve açıklamaları Türkçe

### Hata Yönetim Stratejisi
- **Graceful Error Handling** - Sistem çökmeden hata yönetimi
- **Comprehensive Error Tracking** - Tüm POST istekleri için kapsamlı hata takibi
- **Fallback Mechanisms** - REST API başarısız olduğunda SQL yöntemine geçiş
- **Transaction Management** - Veritabanı işlemlerinde atomik operasyonlar
- **Error Recovery** - Hata kurtarma mekanizmaları
- **Operational Visibility** - İşletimsel görünürlük için detaylı loglama

## API Dokümantasyonu

### Swagger/OpenAPI Entegrasyonu
- **Endpoint:** `/v1/api-docs`
- **Interactive UI:** Swagger UI Express
- **Auto-generation:** Swagger JSDoc ile otomatik dokümantasyon

### Ana API Endpoints
```
GET  /v1/                           # API bilgileri
POST /v1/auth/login                 # Kullanıcı girişi
POST /v1/auth/logout                # Kullanıcı çıkışı
GET  /v1/cariler                    # Cari hesap listesi
POST /v1/cariler                    # Yeni cari hesap
GET  /v1/stoklar                    # Stok listesi
POST /v1/satis-faturalari          # Satış faturası oluştur
POST /v1/satinalma-faturalari      # Satın alma faturası oluştur
```

## Güvenlik

### Kimlik Doğrulama
- **Session-based Authentication** - Express Session
- **Password Hashing** - Güvenli şifre saklama
- **Session Timeout** - Otomatik oturum sonlandırma

### Güvenlik Middleware
- **Helmet** - HTTP güvenlik başlıkları
- **CORS** - Cross-Origin Resource Sharing
- **Rate Limiting** - İstek sınırlama
- **Input Validation** - Zod ile veri doğrulama

## Performans ve Ölçeklenebilirlik

### Cluster Yönetimi
- **Multi-process Architecture** - Node.js Cluster API
- **Worker Process Management** - CPU çekirdek sayısına göre otomatik ölçekleme
- **Graceful Shutdown** - Güvenli sistem kapatma

### Veritabanı Optimizasyonu
- **Connection Pooling** - MSSQL bağlantı havuzu
- **Query Optimization** - Optimize edilmiş SQL sorguları
- **Index Management** - Performans için indeks kullanımı

## Geliştirme Rehberi

### Kod Standartları
- **TypeScript** - Tip güvenliği
- **ESLint** - Kod kalitesi kontrolü
- **Functional Programming** - Fonksiyonel programlama tercihi
- **RORO Pattern** - Receive Object, Return Object
- **Early Return** - Erken dönüş ile basitleştirilmiş mantık

### Proje Yapısı
```
src/
├── shared/                 # Paylaşılan servisler ve yardımcılar
├── rest-api/              # API routes ve middleware
├── [module-name]/         # İş modülleri
│   ├── models/           # Veri modelleri
│   ├── routes/           # API rotaları
│   └── services/         # İş mantığı servisleri
└── scripts/              # Yardımcı scriptler
```

## Gelecek Geliştirme Yol Haritası

### Kısa Vadeli Hedefler
1. **Logo REST API Entegrasyonu** - Tüm modüller için REST API desteği
2. **Error Recovery** - Gelişmiş hata kurtarma mekanizmaları
3. **Performance Monitoring** - Performans izleme ve raporlama
4. **Unit Testing** - Kapsamlı test coverage

### Uzun Vadeli Hedefler
1. **Frontend Development** - React tabanlı yönetim paneli
2. **Real-time Sync** - Gerçek zamanlı veri senkronizasyonu
3. **Enhanced Multi-tenant Features** - Gelişmiş çoklu müşteri özellikleri
4. **Cloud Deployment** - Bulut ortamı dağıtımı
5. **Advanced Analytics** - Entegrasyon performans analitikleri
6. **Automated Error Recovery** - Otomatik hata kurtarma sistemleri

## Kurulum ve Çalıştırma

### Gereksinimler
- Node.js (Latest LTS)
- PNPM
- Microsoft SQL Server
- TypeScript

### Kurulum Adımları
```bash
# Bağımlılıkları yükle
pnpm install

# Geliştirme ortamında çalıştır
pnpm dev

# Production build
pnpm build

# Linting
pnpm lint:fix
```

### Konfigürasyon
1. `config.json` dosyasını düzenle
2. Veritabanı bağlantı bilgilerini güncelle
3. Logo ERP bağlantı ayarlarını yapılandır
4. HTTPS sertifikalarını ayarla (opsiyonel)

## Logo Entegrasyon Loglama Sistemi Implementasyonu

### Uygulanan Değişiklikler

#### 1. Veritabanı Şema Güncellemeleri
Tüm iş modülü tablolarına yeni loglama sütunları eklendi:
```sql
ALTER TABLE CariHesaplar ADD logo_rest_data VARCHAR(MAX) NULL;
ALTER TABLE CariHesaplar ADD logo_sql_data VARCHAR(MAX) NULL;
ALTER TABLE SatisFaturalari ADD logo_rest_data VARCHAR(MAX) NULL;
ALTER TABLE SatisFaturalari ADD logo_sql_data VARCHAR(MAX) NULL;
-- Diğer tablolar için de aynı şekilde...
```

#### 2. Yardımcı Fonksiyonlar
`src/shared/utils/logo-logging-utils.ts` dosyasında:
- `createLogoRestLogData()` - REST API loglama verisi oluşturur
- `createLogoSqlLogData()` - SQL loglama verisi oluşturur
- `createLogoTableName()` - Logo tablo adı formatı oluşturur
- `createCariHesapSqlLogData()` - Cari hesap SQL log verisi
- `createSatisFaturaSqlLogData()` - Satış faturası SQL log verisi

#### 3. Service Layer Güncellemeleri
**Logo REST Service'lerde:**
- `postCariHesap()` fonksiyonu artık `logoRestLogData` döndürüyor
- Hata durumunda da loglama verisi oluşturuluyor

**Logo SQL Service'lerde:**
- `insertClCard()` fonksiyonu `logoSqlLogData` döndürüyor
- `insertCariHesaplar()` fonksiyonu loglama parametreleri alıyor

#### 4. Otomatik Migration
`src/shared/database/queries/logo-logging-migration.ts` ile:
- Sistem başlatılırken otomatik olarak loglama sütunları ekleniyor
- Mevcut tablolar kontrol ediliyor, eksik sütunlar tamamlanıyor

### Kullanım Örnekleri

#### REST API Loglama
```typescript
const response = await LogoRestService.postCariHesap({
  accessToken,
  cariHesap: readyCustomerToPost,
  veritabaniId,
})
// response.logoRestLogData otomatik oluşturuluyor
```

#### SQL Loglama
```typescript
const clcardResult = await LogoSqlService.insertClCard({
  veritabaniId,
  params: clcardParams
})
// clcardResult.logoSqlLogData otomatik oluşturuluyor
```

#### Veritabanına Kaydetme
```typescript
await LogoSqlService.insertCariHesaplar({
  veritabaniId,
  params: cariHesapData,
  logoRestLogData: response.logoRestLogData,
  logoSqlLogData: clcardResult.logoSqlLogData
})
```

### Gelecek Adımlar

#### Tamamlanması Gerekenler
1. **Diğer Modüller:** Satış faturası ve satın alma faturası modüllerinde aynı loglama sistemini uygulama
2. **Test Senaryoları:** JSON loglama format validation testleri yazma
3. **Monitoring:** Loglama verilerini analiz eden dashboard geliştirme
4. **Cleanup:** Eski LogoInvoice, LogoStfiche, LogoStline tablolarını kaldırma

#### Performans Optimizasyonları
1. **JSON Compression:** Büyük JSON verilerini sıkıştırma
2. **Archiving:** Eski loglama verilerini arşivleme
3. **Indexing:** JSON verilerinde arama için index oluşturma

---

**Not:** Bu dokümantasyon, VNS Mizan REST Service projesinin mevcut durumunu yansıtmaktadır. Proje aktif geliştirme aşamasında olduğu için düzenli güncellemeler yapılacaktır. Yeni loglama sistemi cariler modülünde uygulanmış olup, diğer modüllerde de aynı yaklaşım kullanılacaktır.
