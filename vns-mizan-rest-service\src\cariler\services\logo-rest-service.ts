import type { LogoCariHesapRequest } from '../models/types.ts'
import { consola } from 'consola'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'
import { createLogoRestLogData } from '../../shared/utils/logo-logging-utils.ts'

/**
 * Service for handling Logo REST API operations for Cari hesaplar
 */
const LogoRestService = {
  getToken: async ({ veritabaniId }: { veritabaniId: string }): Promise<string> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const restApiUrl = logoConfig.erp.rest_settings.rest_api_url
    const username = logoConfig.erp.kullanici_adi
    const password = logoConfig.erp.sifre
    const firmno = logoConfig.erp.firma_numarasi
    const clientKey = logoConfig.erp.rest_settings.client_key

    try {
      const response = await fetch(`${restApiUrl}/token`, {
        method: 'POST',
        body: `grant_type=password&username=${username}&firmno=${firmno}&password=${password}`,
        headers: { 'Content-Type': 'application/x-www-form-urlencoded', 'Authorization': `Basic ${clientKey}` },
      })
      const data = (await response.json()) as { access_token: string }
      if (!response.ok)
        throw new Error(`HTTP hatası! Durum: ${response.status} ${response.statusText}  ${JSON.stringify(data)}`)
      if (!data.access_token)
        throw new Error('Logo Rest Servisi Hatası - getToken: access_token alınamadı!')
      return data.access_token
    }
    catch (error) {
      consola.error('Logo Rest Servisi Hatası - getToken:', error)
      throw error
    }
  },
  revokeToken: async ({ accessToken, veritabaniId }: { accessToken: string, veritabaniId: string }): Promise<void> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const response = await fetch(`${logoConfig.erp.rest_settings.rest_api_url}/revoke`, { method: 'GET', headers: { Authorization: `Bearer ${accessToken}` } })
      const data = await response.text()
      if (!response.ok)
        throw new Error(`HTTP hatası! Durum: ${response.status} ${response.statusText} ${data}`)
    }
    catch (error) {
      consola.error('Logo Rest Servisi Hatası - revokeToken:', error)
      throw error
    }
  },

  postCariHesap: async ({
    accessToken,
    cariHesap,
    veritabaniId
  }: {
    accessToken: string
    cariHesap: LogoCariHesapRequest
    veritabaniId: string
  }): Promise<{
    INTERNAL_REFERENCE: number
    logoRestLogData: string
  }> => {
    const endpoint = '/Arps'
    let responseStatus: number | undefined
    let responseData: any
    let error: string | undefined

    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const fullUrl = `${logoConfig.erp.rest_settings.rest_api_url}${endpoint}`

      const response = await fetch(fullUrl, {
        method: 'POST',
        body: JSON.stringify(cariHesap),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
      })

      responseStatus = response.status
      responseData = await response.json()

      if (!response.ok) {
        error = `HTTP hatası! Durum: ${response.status} ${response.statusText} ${JSON.stringify(responseData)}`
        throw new Error(error)
      }

      // Başarılı durumda loglama verisi oluştur
      const logoRestLogData = createLogoRestLogData({
        endpoint,
        method: 'POST',
        payload: cariHesap,
        responseStatus,
        responseData
      })

      return {
        INTERNAL_REFERENCE: responseData.INTERNAL_REFERENCE,
        logoRestLogData
      }
    }
    catch (catchError) {
      error = catchError instanceof Error ? catchError.message : 'Bilinmeyen hata'
      consola.error('Logo Rest Servisi Hatası - postCariHesap:', catchError)

      // Hata durumunda da loglama verisi oluştur
      const logoRestLogData = createLogoRestLogData({
        endpoint,
        method: 'POST',
        payload: cariHesap,
        responseStatus,
        responseData,
        error
      })

      // Hata ile birlikte loglama verisini de döndür
      const enhancedError = new Error(error) as Error & { logoRestLogData?: string }
      enhancedError.logoRestLogData = logoRestLogData
      throw enhancedError
    }
  },
}

export default LogoRestService
