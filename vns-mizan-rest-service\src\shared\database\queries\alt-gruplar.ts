/**
 * SQL queries for 'AltGruplar' module initialization and operations
 */
export const query = `
-- Table: AltGruplar
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AltGruplar' and xtype='U')
BEGIN
    CREATE TABLE AltGruplar (
        id INT PRIMARY KEY IDENTITY(1,1),
        kodu VARCHAR(25) NOT NULL,
        adi VARCHAR(100) NOT NULL,
        aciklama VARCHAR(250),
        veritabani_id VARCHAR(37) NOT NULL,
        logoRef INT,
        error NVARCHAR(MAX),
        logo_rest_data VARCHAR(MAX) NULL,
        logo_sql_data VARCHAR(MAX) NULL,
        createdAt DATETIME2 NOT NULL DEFAULT GETDATE(),
        updatedAt DATETIME2 NOT NULL DEFAULT GETDATE()
    );
    
    CREATE UNIQUE INDEX IX_AltGruplar_kodu_veritabani ON AltGruplar(kodu, veritabani_id);
    PRINT 'AltGruplar tablosu başarıyla oluşturuldu';
END
ELSE
BEGIN
    PRINT 'AltGruplar tablosu zaten mevcut';
END;
`
