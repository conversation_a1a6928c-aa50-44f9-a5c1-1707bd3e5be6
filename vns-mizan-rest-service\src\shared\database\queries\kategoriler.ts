/**
 * SQL queries for 'Kategoriler' module initialization and operations
 */
export const query = `
-- Table: Kategoriler
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Kategoriler' and xtype='U')
BEGIN
    CREATE TABLE Kategoriler (
        id INT PRIMARY KEY IDENTITY(1,1),
        kodu VARCHAR(25) NOT NULL,
        adi VARCHAR(100) NOT NULL,
        aciklama VARCHAR(250),
        veritabani_id VARCHAR(37) NOT NULL,
        logoRef INT,
        error NVARCHAR(MAX),
        logo_rest_data VARCHAR(MAX) NULL,
        logo_sql_data VARCHAR(MAX) NULL,
        createdAt DATETIME2 NOT NULL DEFAULT GETDATE(),
        updatedAt DATETIME2 NOT NULL DEFAULT GETDATE()
    );
    
    CREATE UNIQUE INDEX IX_Kategoriler_kodu_veritabani ON Kategoriler(kodu, veritabani_id);
    PRINT 'Kategoriler tablosu ba<PERSON>ar<PERSON>yla oluşturuldu';
END
ELSE
BEGIN
    PRINT 'Kategoriler tablosu zaten mevcut';
END;
`
